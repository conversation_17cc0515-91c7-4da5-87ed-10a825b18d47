-- ===================================================================
-- استعلامات مفيدة لإدارة قاعدة بيانات بوت نشر المودات
-- ===================================================================

-- ===================================================================
-- استعلامات التحقق من سلامة البيانات
-- ===================================================================

-- التحقق من وجود مستخدمين بدون قنوات
SELECT u.user_id, u.username, u.full_name, u.created_at
FROM users u
LEFT JOIN user_channels uc ON u.user_id = uc.user_id
WHERE uc.user_id IS NULL;

-- التحقق من القنوات بدون مستخدمين (بيانات يتيمة)
SELECT uc.channel_id, uc.channel_name, uc.created_at
FROM user_channels uc
LEFT JOIN users u ON uc.user_id = u.user_id
WHERE u.user_id IS NULL;

-- التحقق من المستخدمين الذين لديهم أكثر من قناة افتراضية واحدة
SELECT user_id, COUNT(*) as default_channels_count
FROM user_channels
WHERE is_default = true
GROUP BY user_id
HAVING COUNT(*) > 1;

-- ===================================================================
-- استعلامات الإحصائيات
-- ===================================================================

-- إحصائيات المستخدمين
SELECT 
    COUNT(*) as total_users,
    COUNT(CASE WHEN is_active = true THEN 1 END) as active_users,
    COUNT(CASE WHEN last_activity > CURRENT_TIMESTAMP - INTERVAL '7 days' THEN 1 END) as users_last_week,
    COUNT(CASE WHEN created_at > CURRENT_TIMESTAMP - INTERVAL '30 days' THEN 1 END) as new_users_last_month
FROM users;

-- إحصائيات القنوات
SELECT 
    COUNT(*) as total_channels,
    COUNT(CASE WHEN is_active = true THEN 1 END) as active_channels,
    COUNT(DISTINCT user_id) as users_with_channels,
    AVG(publish_interval) as avg_publish_interval
FROM user_channels;

-- إحصائيات المنشورات المعلقة
SELECT 
    status,
    COUNT(*) as count
FROM pending_publications
GROUP BY status;

-- أكثر المودات نشراً
SELECT 
    m.name,
    m.category,
    COUNT(ps.id) as publications_count,
    SUM(ps.downloads_count) as total_downloads
FROM mods m
JOIN publication_stats ps ON m.mod_id = ps.mod_id
GROUP BY m.mod_id, m.name, m.category
ORDER BY publications_count DESC
LIMIT 10;

-- ===================================================================
-- استعلامات الصيانة
-- ===================================================================

-- حذف المنشورات المعلقة القديمة (أكثر من 30 يوم)
-- تم تعطيل الشرط الزمني مؤقتاً بسبب عدم تطابق مخطط قاعدة البيانات
DELETE FROM pending_publications 
WHERE status IN ('rejected', 'approved');

-- حذف سجلات الأنشطة القديمة (أكثر من 90 يوم)
DELETE FROM activity_logs 
WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '90 days';

-- تحديث آخر نشاط للمستخدمين النشطين
UPDATE users 
SET last_activity = CURRENT_TIMESTAMP 
WHERE user_id IN (
    SELECT DISTINCT user_id 
    FROM activity_logs 
    WHERE created_at > CURRENT_TIMESTAMP - INTERVAL '1 day'
);

-- ===================================================================
-- استعلامات إصلاح البيانات
-- ===================================================================

-- إصلاح المستخدمين بدون قناة افتراضية
UPDATE user_channels 
SET is_default = true 
WHERE id IN (
    SELECT DISTINCT ON (user_id) id
    FROM user_channels uc1
    WHERE NOT EXISTS (
        SELECT 1 FROM user_channels uc2 
        WHERE uc2.user_id = uc1.user_id AND uc2.is_default = true
    )
    ORDER BY user_id, created_at ASC
);

-- إصلاح القنوات المكررة كافتراضية
UPDATE user_channels 
SET is_default = false 
WHERE id NOT IN (
    SELECT DISTINCT ON (user_id) id
    FROM user_channels
    WHERE is_default = true
    ORDER BY user_id, created_at ASC
);

-- ===================================================================
-- استعلامات البحث والتصفية
-- ===================================================================

-- البحث عن مستخدم بالاسم أو اليوزرنيم
SELECT u.*, 
       COUNT(uc.id) as channels_count,
       MAX(uc.last_publish_time) as last_publish
FROM users u
LEFT JOIN user_channels uc ON u.user_id = uc.user_id
WHERE u.username ILIKE '%search_term%' 
   OR u.full_name ILIKE '%search_term%'
   OR u.user_id = 'search_term'
GROUP BY u.user_id;

-- البحث عن قناة بالمعرف أو الاسم
SELECT uc.*, u.username, u.full_name
FROM user_channels uc
JOIN users u ON uc.user_id = u.user_id
WHERE uc.channel_id ILIKE '%search_term%' 
   OR uc.channel_name ILIKE '%search_term%';

-- المستخدمين الأكثر نشاطاً
SELECT u.user_id, u.username, u.full_name,
       COUNT(ps.id) as total_publications,
       MAX(ps.published_at) as last_publication,
       COUNT(DISTINCT uc.channel_id) as channels_count
FROM users u
LEFT JOIN user_channels uc ON u.user_id = uc.user_id
LEFT JOIN publication_stats ps ON u.user_id = ps.user_id
WHERE u.is_active = true
GROUP BY u.user_id, u.username, u.full_name
ORDER BY total_publications DESC, last_publication DESC
LIMIT 20;

-- ===================================================================
-- استعلامات التقارير
-- ===================================================================

-- تقرير يومي للنشاط
SELECT 
    DATE(created_at) as date,
    COUNT(*) as new_users,
    COUNT(CASE WHEN lang = 'ar' THEN 1 END) as arabic_users,
    COUNT(CASE WHEN lang = 'en' THEN 1 END) as english_users
FROM users
WHERE created_at > CURRENT_TIMESTAMP - INTERVAL '30 days'
GROUP BY DATE(created_at)
ORDER BY date DESC;

-- تقرير أداء القنوات
SELECT 
    uc.channel_id,
    uc.channel_name,
    u.username,
    COUNT(ps.id) as total_publications,
    AVG(ps.downloads_count) as avg_downloads,
    MAX(ps.published_at) as last_publication,
    uc.publish_interval,
    uc.is_active
FROM user_channels uc
JOIN users u ON uc.user_id = u.user_id
LEFT JOIN publication_stats ps ON uc.channel_id = ps.channel_id
GROUP BY uc.id, uc.channel_id, uc.channel_name, u.username, uc.publish_interval, uc.is_active
ORDER BY total_publications DESC;

-- ===================================================================
-- استعلامات النسخ الاحتياطي
-- ===================================================================

-- تصدير بيانات المستخدمين (للنسخ الاحتياطي)
SELECT 
    u.user_id,
    u.username,
    u.full_name,
    u.lang,
    u.is_active,
    u.created_at,
    json_agg(
        json_build_object(
            'channel_id', uc.channel_id,
            'channel_name', uc.channel_name,
            'is_default', uc.is_default,
            'is_active', uc.is_active,
            'publish_interval', uc.publish_interval,
            'channel_lang', uc.channel_lang,
            'message_format', uc.message_format
        )
    ) as channels
FROM users u
LEFT JOIN user_channels uc ON u.user_id = uc.user_id
GROUP BY u.user_id, u.username, u.full_name, u.lang, u.is_active, u.created_at
ORDER BY u.created_at DESC;

-- ===================================================================
-- استعلامات الأمان
-- ===================================================================

-- المستخدمين المشبوهين (نشاط غير طبيعي)
SELECT u.user_id, u.username, u.full_name,
       COUNT(ps.id) as publications_today,
       COUNT(DISTINCT ps.channel_id) as channels_used_today
FROM users u
JOIN publication_stats ps ON u.user_id = ps.user_id
WHERE ps.published_at > CURRENT_TIMESTAMP - INTERVAL '1 day'
GROUP BY u.user_id, u.username, u.full_name
HAVING COUNT(ps.id) > 50 OR COUNT(DISTINCT ps.channel_id) > 10
ORDER BY publications_today DESC;

-- القنوات غير النشطة لفترة طويلة
SELECT uc.channel_id, uc.channel_name, u.username,
       uc.last_publish_time,
       EXTRACT(DAYS FROM (CURRENT_TIMESTAMP - uc.last_publish_time)) as days_inactive
FROM user_channels uc
JOIN users u ON uc.user_id = u.user_id
WHERE uc.is_active = true 
  AND (uc.last_publish_time IS NULL OR uc.last_publish_time < CURRENT_TIMESTAMP - INTERVAL '30 days')
ORDER BY days_inactive DESC NULLS FIRST;
