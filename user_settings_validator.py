# ===================================================================
# مدقق إعدادات المستخدمين - User Settings Validator
# ===================================================================
# نظام شامل للتحقق من عمل جميع إعدادات المستخدمين
# يضمن عدم فقدان البيانات بعد تحديث الصفحة أو النظام
# ===================================================================

import os
import json
import logging
import requests
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any, Tuple
from user_database_client import *
from supabase_client import get_user_page_customization_settings, update_user_page_customization

# إعداد التسجيل
logger = logging.getLogger(__name__)

# ===================================================================
# إعدادات التحقق
# ===================================================================

class ValidationConfig:
    # إعدادات المستخدم المطلوب فحصها
    USER_SETTINGS_FIELDS = [
        'user_id', 'username', 'full_name', 'lang', 
        'first_seen', 'last_activity', 'is_active'
    ]
    
    # إعدادات القناة المطلوب فحصها
    CHANNEL_SETTINGS_FIELDS = [
        'user_id', 'channel_id', 'channel_name', 'is_default',
        'is_active', 'publish_interval', 'preview_enabled',
        'message_format', 'channel_lang'
    ]
    
    # إعدادات تخصيص الصفحة
    PAGE_CUSTOMIZATION_FIELDS = [
        'site_name', 'channel_logo_url', 'logo_position',
        'page_theme', 'custom_bg_color', 'custom_header_color',
        'custom_text_color', 'custom_button_color', 'custom_border_color',
        'show_all_images', 'enable_mod_opening',
        'download_button_text_ar', 'download_button_text_en',
        'open_button_text_ar', 'open_button_text_en'
    ]
    
    # إعدادات المميزات
    FEATURE_SETTINGS = [
        'unlimited_channels', 'url_shortener_access', 'custom_download_links',
        'publish_intervals_extended', 'tasks_system_access',
        'page_customization_vip', 'ads_system_access'
    ]
    
    # الحد الأدنى للبيانات المطلوبة
    MIN_REQUIRED_DATA = {
        'user_id': True,
        'channel_id': True,
        'site_name': 'Modetaris'
    }

# ===================================================================
# مدقق إعدادات المستخدمين
# ===================================================================

class UserSettingsValidator:
    def __init__(self):
        self.validation_results = {}
        self.errors = []
        self.warnings = []
        self.fixed_issues = []
    
    def validate_user_data(self, user_id: str) -> Dict[str, Any]:
        """التحقق من بيانات المستخدم الأساسية"""
        try:
            logger.info(f"🔍 فحص بيانات المستخدم {user_id}")
            
            # جلب بيانات المستخدم من قاعدة البيانات الجديدة
            user_data = get_user(user_id)
            
            validation_result = {
                'user_id': user_id,
                'exists_in_new_db': bool(user_data),
                'data_complete': False,
                'missing_fields': [],
                'data': user_data
            }
            
            if user_data:
                # فحص الحقول المطلوبة
                missing_fields = []
                for field in ValidationConfig.USER_SETTINGS_FIELDS:
                    if field not in user_data or user_data[field] is None:
                        missing_fields.append(field)
                
                validation_result['missing_fields'] = missing_fields
                validation_result['data_complete'] = len(missing_fields) == 0
                
                if missing_fields:
                    self.warnings.append(f"المستخدم {user_id}: حقول مفقودة {missing_fields}")
                else:
                    logger.info(f"✅ بيانات المستخدم {user_id} مكتملة")
            else:
                self.errors.append(f"المستخدم {user_id} غير موجود في قاعدة البيانات الجديدة")
            
            return validation_result
            
        except Exception as e:
            error_msg = f"خطأ في فحص بيانات المستخدم {user_id}: {e}"
            logger.error(error_msg)
            self.errors.append(error_msg)
            return {'user_id': user_id, 'error': str(e)}
    
    def validate_channel_settings(self, user_id: str) -> Dict[str, Any]:
        """التحقق من إعدادات القنوات"""
        try:
            logger.info(f"🔍 فحص إعدادات القنوات للمستخدم {user_id}")
            
            # جلب قنوات المستخدم من قاعدة البيانات الجديدة
            channels = get_user_channels(user_id)
            
            validation_result = {
                'user_id': user_id,
                'total_channels': len(channels),
                'channels_data': [],
                'has_default_channel': False,
                'all_channels_valid': True
            }
            
            default_channel_count = 0
            
            for channel in channels:
                channel_validation = {
                    'channel_id': channel.get('channel_id'),
                    'is_valid': True,
                    'missing_fields': [],
                    'has_categories': bool(channel.get('mod_categories')),
                    'has_versions': bool(channel.get('mc_versions'))
                }
                
                # فحص الحقول المطلوبة
                missing_fields = []
                for field in ValidationConfig.CHANNEL_SETTINGS_FIELDS:
                    if field not in channel or channel[field] is None:
                        missing_fields.append(field)
                
                channel_validation['missing_fields'] = missing_fields
                channel_validation['is_valid'] = len(missing_fields) == 0
                
                if not channel_validation['is_valid']:
                    validation_result['all_channels_valid'] = False
                    self.warnings.append(f"القناة {channel.get('channel_id')} للمستخدم {user_id}: حقول مفقودة {missing_fields}")
                
                # فحص القناة الافتراضية
                if channel.get('is_default'):
                    default_channel_count += 1
                    validation_result['has_default_channel'] = True
                
                validation_result['channels_data'].append(channel_validation)
            
            # فحص وجود قناة افتراضية واحدة فقط
            if default_channel_count == 0:
                self.warnings.append(f"المستخدم {user_id}: لا توجد قناة افتراضية")
            elif default_channel_count > 1:
                self.warnings.append(f"المستخدم {user_id}: أكثر من قناة افتراضية ({default_channel_count})")
            
            logger.info(f"✅ تم فحص {len(channels)} قناة للمستخدم {user_id}")
            return validation_result
            
        except Exception as e:
            error_msg = f"خطأ في فحص إعدادات القنوات للمستخدم {user_id}: {e}"
            logger.error(error_msg)
            self.errors.append(error_msg)
            return {'user_id': user_id, 'error': str(e)}
    
    def validate_page_customization(self, user_id: str) -> Dict[str, Any]:
        """التحقق من إعدادات تخصيص الصفحة"""
        try:
            logger.info(f"🔍 فحص إعدادات تخصيص الصفحة للمستخدم {user_id}")
            
            # جلب إعدادات التخصيص من قاعدة البيانات القديمة (supabase_client)
            old_customization = get_user_page_customization_settings(user_id)
            
            validation_result = {
                'user_id': user_id,
                'has_customization': bool(old_customization),
                'customization_data': old_customization,
                'missing_fields': [],
                'needs_migration': False,
                'logo_url_valid': True
            }
            
            if old_customization:
                # فحص الحقول المطلوبة
                missing_fields = []
                for field in ValidationConfig.PAGE_CUSTOMIZATION_FIELDS:
                    if field not in old_customization:
                        missing_fields.append(field)
                
                validation_result['missing_fields'] = missing_fields
                
                # فحص صحة رابط الصورة
                logo_url = old_customization.get('channel_logo_url')
                if logo_url:
                    try:
                        response = requests.head(logo_url, timeout=5)
                        validation_result['logo_url_valid'] = response.status_code < 400
                        if not validation_result['logo_url_valid']:
                            self.warnings.append(f"المستخدم {user_id}: رابط صورة القناة غير صالح ({response.status_code})")
                    except Exception:
                        validation_result['logo_url_valid'] = False
                        self.warnings.append(f"المستخدم {user_id}: لا يمكن الوصول لرابط صورة القناة")
                
                # تحديد ما إذا كانت البيانات تحتاج للهجرة
                validation_result['needs_migration'] = True  # سيتم نقلها لقاعدة البيانات الجديدة
                
                logger.info(f"✅ المستخدم {user_id} لديه إعدادات تخصيص")
            else:
                logger.info(f"ℹ️ المستخدم {user_id} ليس لديه إعدادات تخصيص")
            
            return validation_result
            
        except Exception as e:
            error_msg = f"خطأ في فحص إعدادات تخصيص الصفحة للمستخدم {user_id}: {e}"
            logger.error(error_msg)
            self.errors.append(error_msg)
            return {'user_id': user_id, 'error': str(e)}
    
    def validate_feature_settings(self, user_id: str) -> Dict[str, Any]:
        """التحقق من إعدادات المميزات"""
        try:
            logger.info(f"🔍 فحص إعدادات المميزات للمستخدم {user_id}")
            
            # جلب مميزات المستخدم من قاعدة البيانات الجديدة
            features = get_user_features(user_id)
            
            validation_result = {
                'user_id': user_id,
                'total_features': len(features),
                'enabled_features': [],
                'disabled_features': [],
                'features_data': features
            }
            
            # تصنيف المميزات
            for feature_name, is_enabled in features.items():
                if is_enabled:
                    validation_result['enabled_features'].append(feature_name)
                else:
                    validation_result['disabled_features'].append(feature_name)
            
            # فحص المميزات الأساسية
            for feature in ValidationConfig.FEATURE_SETTINGS:
                if feature not in features:
                    self.warnings.append(f"المستخدم {user_id}: الميزة {feature} غير محددة")
            
            logger.info(f"✅ المستخدم {user_id} لديه {len(validation_result['enabled_features'])} ميزة مفعلة")
            return validation_result
            
        except Exception as e:
            error_msg = f"خطأ في فحص إعدادات المميزات للمستخدم {user_id}: {e}"
            logger.error(error_msg)
            self.errors.append(error_msg)
            return {'user_id': user_id, 'error': str(e)}
    
    def fix_user_issues(self, user_id: str, validation_results: Dict[str, Any]) -> bool:
        """إصلاح مشاكل المستخدم المكتشفة"""
        try:
            logger.info(f"🔧 إصلاح مشاكل المستخدم {user_id}")
            
            fixed_count = 0
            
            # إصلاح بيانات المستخدم المفقودة
            user_validation = validation_results.get('user_data', {})
            if not user_validation.get('exists_in_new_db'):
                # إنشاء المستخدم في قاعدة البيانات الجديدة
                success = create_or_update_user(user_id, f"user_{user_id}", f"User {user_id}", "ar")
                if success:
                    self.fixed_issues.append(f"تم إنشاء المستخدم {user_id} في قاعدة البيانات الجديدة")
                    fixed_count += 1
            
            # إصلاح إعدادات القناة الافتراضية
            channel_validation = validation_results.get('channel_settings', {})
            if not channel_validation.get('has_default_channel') and channel_validation.get('total_channels', 0) > 0:
                # تعيين أول قناة كافتراضية
                channels = get_user_channels(user_id)
                if channels:
                    first_channel = channels[0]
                    success = update_channel_settings(user_id, first_channel['channel_id'], is_default=True)
                    if success:
                        self.fixed_issues.append(f"تم تعيين القناة {first_channel['channel_id']} كافتراضية للمستخدم {user_id}")
                        fixed_count += 1
            
            # إصلاح المميزات الأساسية المفقودة
            feature_validation = validation_results.get('feature_settings', {})
            features = feature_validation.get('features_data', {})
            for feature in ValidationConfig.FEATURE_SETTINGS:
                if feature not in features:
                    # تفعيل الميزة بالقيمة الافتراضية
                    success = set_user_feature(user_id, feature, True, None, "auto_fix")
                    if success:
                        self.fixed_issues.append(f"تم تفعيل الميزة {feature} للمستخدم {user_id}")
                        fixed_count += 1
            
            logger.info(f"✅ تم إصلاح {fixed_count} مشكلة للمستخدم {user_id}")
            return fixed_count > 0
            
        except Exception as e:
            error_msg = f"خطأ في إصلاح مشاكل المستخدم {user_id}: {e}"
            logger.error(error_msg)
            self.errors.append(error_msg)
            return False
    
    def validate_all_users(self, user_ids: List[str] = None) -> Dict[str, Any]:
        """فحص شامل لجميع المستخدمين أو قائمة محددة"""
        try:
            logger.info("🚀 بدء الفحص الشامل لإعدادات المستخدمين")
            
            # إذا لم يتم تمرير قائمة مستخدمين، جلب جميع المستخدمين
            if user_ids is None:
                all_users = get_all_users()
                user_ids = [user['user_id'] for user in all_users]
            
            total_users = len(user_ids)
            logger.info(f"📊 سيتم فحص {total_users} مستخدم")
            
            # نتائج الفحص الشامل
            comprehensive_results = {
                'total_users': total_users,
                'users_validated': 0,
                'users_with_issues': 0,
                'users_fixed': 0,
                'validation_details': {},
                'summary': {
                    'users_missing_from_new_db': 0,
                    'users_without_channels': 0,
                    'users_without_default_channel': 0,
                    'users_with_customization': 0,
                    'users_with_broken_logos': 0,
                    'total_errors': 0,
                    'total_warnings': 0,
                    'total_fixes': 0
                }
            }
            
            # فحص كل مستخدم
            for i, user_id in enumerate(user_ids, 1):
                logger.info(f"📋 فحص المستخدم {i}/{total_users}: {user_id}")
                
                user_results = {
                    'user_data': self.validate_user_data(user_id),
                    'channel_settings': self.validate_channel_settings(user_id),
                    'page_customization': self.validate_page_customization(user_id),
                    'feature_settings': self.validate_feature_settings(user_id)
                }
                
                # تحديد ما إذا كان المستخدم يحتاج إصلاح
                has_issues = (
                    not user_results['user_data'].get('exists_in_new_db', False) or
                    not user_results['channel_settings'].get('has_default_channel', False) or
                    not user_results['page_customization'].get('logo_url_valid', True)
                )
                
                if has_issues:
                    comprehensive_results['users_with_issues'] += 1
                    
                    # محاولة إصلاح المشاكل
                    if self.fix_user_issues(user_id, user_results):
                        comprehensive_results['users_fixed'] += 1
                
                # تحديث الإحصائيات
                if not user_results['user_data'].get('exists_in_new_db', False):
                    comprehensive_results['summary']['users_missing_from_new_db'] += 1
                
                if user_results['channel_settings'].get('total_channels', 0) == 0:
                    comprehensive_results['summary']['users_without_channels'] += 1
                
                if not user_results['channel_settings'].get('has_default_channel', False):
                    comprehensive_results['summary']['users_without_default_channel'] += 1
                
                if user_results['page_customization'].get('has_customization', False):
                    comprehensive_results['summary']['users_with_customization'] += 1
                
                if not user_results['page_customization'].get('logo_url_valid', True):
                    comprehensive_results['summary']['users_with_broken_logos'] += 1
                
                comprehensive_results['validation_details'][user_id] = user_results
                comprehensive_results['users_validated'] += 1
            
            # تحديث الإحصائيات النهائية
            comprehensive_results['summary']['total_errors'] = len(self.errors)
            comprehensive_results['summary']['total_warnings'] = len(self.warnings)
            comprehensive_results['summary']['total_fixes'] = len(self.fixed_issues)
            
            logger.info("✅ تم إكمال الفحص الشامل")
            return comprehensive_results
            
        except Exception as e:
            error_msg = f"خطأ في الفحص الشامل: {e}"
            logger.error(error_msg)
            self.errors.append(error_msg)
            return {'error': str(e)}
    
    def generate_report(self, validation_results: Dict[str, Any]) -> str:
        """إنشاء تقرير مفصل عن نتائج الفحص"""
        try:
            report_lines = []
            report_lines.append("=" * 60)
            report_lines.append("📊 تقرير فحص إعدادات المستخدمين")
            report_lines.append("=" * 60)
            
            # الإحصائيات العامة
            summary = validation_results.get('summary', {})
            report_lines.append(f"📈 إجمالي المستخدمين: {validation_results.get('total_users', 0)}")
            report_lines.append(f"✅ تم فحصهم: {validation_results.get('users_validated', 0)}")
            report_lines.append(f"⚠️ يحتاجون إصلاح: {validation_results.get('users_with_issues', 0)}")
            report_lines.append(f"🔧 تم إصلاحهم: {validation_results.get('users_fixed', 0)}")
            report_lines.append("")
            
            # تفاصيل المشاكل
            report_lines.append("🔍 تفاصيل المشاكل المكتشفة:")
            report_lines.append(f"   • مستخدمون مفقودون من قاعدة البيانات الجديدة: {summary.get('users_missing_from_new_db', 0)}")
            report_lines.append(f"   • مستخدمون بدون قنوات: {summary.get('users_without_channels', 0)}")
            report_lines.append(f"   • مستخدمون بدون قناة افتراضية: {summary.get('users_without_default_channel', 0)}")
            report_lines.append(f"   • مستخدمون لديهم تخصيص صفحة: {summary.get('users_with_customization', 0)}")
            report_lines.append(f"   • مستخدمون لديهم صور معطلة: {summary.get('users_with_broken_logos', 0)}")
            report_lines.append("")
            
            # الأخطاء والتحذيرات
            report_lines.append(f"❌ إجمالي الأخطاء: {summary.get('total_errors', 0)}")
            report_lines.append(f"⚠️ إجمالي التحذيرات: {summary.get('total_warnings', 0)}")
            report_lines.append(f"🔧 إجمالي الإصلاحات: {summary.get('total_fixes', 0)}")
            report_lines.append("")
            
            # قائمة الأخطاء
            if self.errors:
                report_lines.append("❌ الأخطاء:")
                for error in self.errors[:10]:  # أول 10 أخطاء
                    report_lines.append(f"   • {error}")
                if len(self.errors) > 10:
                    report_lines.append(f"   ... و {len(self.errors) - 10} خطأ آخر")
                report_lines.append("")
            
            # قائمة التحذيرات
            if self.warnings:
                report_lines.append("⚠️ التحذيرات:")
                for warning in self.warnings[:10]:  # أول 10 تحذيرات
                    report_lines.append(f"   • {warning}")
                if len(self.warnings) > 10:
                    report_lines.append(f"   ... و {len(self.warnings) - 10} تحذير آخر")
                report_lines.append("")
            
            # قائمة الإصلاحات
            if self.fixed_issues:
                report_lines.append("🔧 الإصلاحات المطبقة:")
                for fix in self.fixed_issues[:10]:  # أول 10 إصلاحات
                    report_lines.append(f"   • {fix}")
                if len(self.fixed_issues) > 10:
                    report_lines.append(f"   ... و {len(self.fixed_issues) - 10} إصلاح آخر")
                report_lines.append("")
            
            report_lines.append("=" * 60)
            report_lines.append(f"📅 تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            report_lines.append("=" * 60)
            
            return "\n".join(report_lines)
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء التقرير: {e}")
            return f"خطأ في إنشاء التقرير: {e}"

# ===================================================================
# مدقق الإعدادات العالمي
# ===================================================================

# إنشاء مثيل عالمي
settings_validator = UserSettingsValidator()

# ===================================================================
# دوال مساعدة
# ===================================================================

def validate_single_user(user_id: str) -> Dict[str, Any]:
    """فحص مستخدم واحد"""
    return settings_validator.validate_all_users([user_id])

def validate_all_users_quick() -> Dict[str, Any]:
    """فحص سريع لجميع المستخدمين"""
    return settings_validator.validate_all_users()

def generate_validation_report(validation_results: Dict[str, Any]) -> str:
    """إنشاء تقرير الفحص"""
    return settings_validator.generate_report(validation_results)
