#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل للبوت
يتحقق من جميع الوظائف الأساسية والتحسينات الجديدة
"""

import os
import sys
import asyncio
import logging
import time
import gc
from datetime import datetime

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class BotTester:
    """فئة اختبار البوت الشامل"""
    
    def __init__(self):
        self.test_results = {}
        self.total_tests = 0
        self.passed_tests = 0
        self.failed_tests = 0
        
    def log_test_result(self, test_name, success, message=""):
        """تسجيل نتيجة الاختبار"""
        self.total_tests += 1
        if success:
            self.passed_tests += 1
            status = "✅ نجح"
        else:
            self.failed_tests += 1
            status = "❌ فشل"
            
        self.test_results[test_name] = {
            'success': success,
            'message': message,
            'status': status
        }
        
        logger.info(f"{status} - {test_name}: {message}")
    
    def test_imports(self):
        """اختبار الاستيرادات"""
        try:
            # اختبار استيراد الملفات الأساسية
            import main
            self.log_test_result("استيراد main.py", True, "تم بنجاح")
            
            import supabase_client
            self.log_test_result("استيراد supabase_client.py", True, "تم بنجاح")
            
            # اختبار استيراد المكتبات المطلوبة
            import telegram
            self.log_test_result("استيراد telegram", True, "تم بنجاح")
            
            import requests
            self.log_test_result("استيراد requests", True, "تم بنجاح")
            
            return True
            
        except Exception as e:
            self.log_test_result("الاستيرادات", False, f"خطأ: {e}")
            return False
    
    def test_environment_variables(self):
        """اختبار متغيرات البيئة"""
        try:
            from dotenv import load_dotenv
            load_dotenv()
            
            required_vars = ['BOT_TOKEN', 'ADMIN_CHAT_ID', 'SUPABASE_URL', 'SUPABASE_KEY']
            missing_vars = []
            
            for var in required_vars:
                if not os.getenv(var):
                    missing_vars.append(var)
            
            if missing_vars:
                self.log_test_result("متغيرات البيئة", False, f"متغيرات مفقودة: {missing_vars}")
                return False
            else:
                self.log_test_result("متغيرات البيئة", True, "جميع المتغيرات موجودة")
                return True
                
        except Exception as e:
            self.log_test_result("متغيرات البيئة", False, f"خطأ: {e}")
            return False
    
    def test_database_connection(self):
        """اختبار الاتصال بقاعدة البيانات"""
        try:
            from supabase_client import safe_supabase_request, SUPABASE_URL, SUPABASE_KEY
            
            if not SUPABASE_URL or not SUPABASE_KEY:
                self.log_test_result("اتصال قاعدة البيانات", False, "إعدادات Supabase مفقودة")
                return False
            
            # اختبار الاتصال
            test_url = f"{SUPABASE_URL}/rest/v1/mods?limit=1"
            response = safe_supabase_request('GET', test_url)
            
            if response and response.status_code == 200:
                self.log_test_result("اتصال قاعدة البيانات", True, "الاتصال يعمل")
                return True
            else:
                status_code = response.status_code if response else "لا يوجد استجابة"
                self.log_test_result("اتصال قاعدة البيانات", False, f"فشل الاتصال: {status_code}")
                return False
                
        except Exception as e:
            self.log_test_result("اتصال قاعدة البيانات", False, f"خطأ: {e}")
            return False
    
    def test_signal_handling(self):
        """اختبار معالجة الإشارات"""
        try:
            import signal
            from main import signal_handler, setup_signal_handlers
            
            # اختبار إعداد معالجات الإشارات
            setup_signal_handlers()
            self.log_test_result("معالجة الإشارات", True, "تم إعداد معالجات الإشارات")
            return True
            
        except Exception as e:
            self.log_test_result("معالجة الإشارات", False, f"خطأ: {e}")
            return False
    
    def test_memory_management(self):
        """اختبار إدارة الذاكرة"""
        try:
            from main import memory_manager
            
            # اختبار تنظيف الذاكرة
            initial_objects = len(gc.get_objects())
            success = memory_manager.cleanup_memory()
            final_objects = len(gc.get_objects())
            
            if success:
                self.log_test_result("إدارة الذاكرة", True, f"تم تنظيف {initial_objects - final_objects} كائن")
                return True
            else:
                self.log_test_result("إدارة الذاكرة", False, "فشل في تنظيف الذاكرة")
                return False
                
        except Exception as e:
            self.log_test_result("إدارة الذاكرة", False, f"خطأ: {e}")
            return False
    
    def test_health_monitoring(self):
        """اختبار مراقبة الصحة"""
        try:
            from main import health_monitor
            
            # اختبار نبضة الصحة
            health_monitor.heartbeat()
            is_healthy = health_monitor.check_health()
            
            if is_healthy:
                self.log_test_result("مراقبة الصحة", True, "البوت صحي")
                return True
            else:
                self.log_test_result("مراقبة الصحة", False, "البوت غير صحي")
                return False
                
        except Exception as e:
            self.log_test_result("مراقبة الصحة", False, f"خطأ: {e}")
            return False
    
    def test_caching_system(self):
        """اختبار نظام التخزين المؤقت"""
        try:
            from supabase_client import _set_cache, _get_from_cache, _clear_cache
            
            # اختبار حفظ واسترجاع من التخزين المؤقت
            test_channel = "test_channel_123"
            test_link = "https://test.com"
            
            _set_cache(test_channel, test_link)
            retrieved_link = _get_from_cache(test_channel)
            
            if retrieved_link == test_link:
                _clear_cache(test_channel)
                self.log_test_result("نظام التخزين المؤقت", True, "يعمل بشكل صحيح")
                return True
            else:
                self.log_test_result("نظام التخزين المؤقت", False, "فشل في الاسترجاع")
                return False
                
        except Exception as e:
            self.log_test_result("نظام التخزين المؤقت", False, f"خطأ: {e}")
            return False
    
    def test_helper_functions(self):
        """اختبار الدوال المساعدة"""
        try:
            from main import get_user_channel_id, is_channel_setup_complete
            
            # اختبار الدوال المساعدة (مع بيانات وهمية)
            # هذا اختبار أساسي للتأكد من أن الدوال لا تتعطل
            result1 = get_user_channel_id("test_user")
            result2 = is_channel_setup_complete("test_user")
            
            self.log_test_result("الدوال المساعدة", True, "تعمل بدون أخطاء")
            return True
            
        except Exception as e:
            self.log_test_result("الدوال المساعدة", False, f"خطأ: {e}")
            return False
    
    def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        logger.info("🧪 بدء الاختبار الشامل للبوت...")
        logger.info("=" * 50)
        
        # قائمة الاختبارات
        tests = [
            self.test_imports,
            self.test_environment_variables,
            self.test_database_connection,
            self.test_signal_handling,
            self.test_memory_management,
            self.test_health_monitoring,
            self.test_caching_system,
            self.test_helper_functions
        ]
        
        # تشغيل الاختبارات
        for test in tests:
            try:
                test()
            except Exception as e:
                test_name = test.__name__.replace('test_', '').replace('_', ' ')
                self.log_test_result(test_name, False, f"خطأ غير متوقع: {e}")
        
        # عرض النتائج النهائية
        self.show_final_results()
    
    def show_final_results(self):
        """عرض النتائج النهائية"""
        logger.info("=" * 50)
        logger.info("📊 ملخص نتائج الاختبار:")
        logger.info(f"  إجمالي الاختبارات: {self.total_tests}")
        logger.info(f"  نجح: {self.passed_tests}")
        logger.info(f"  فشل: {self.failed_tests}")
        logger.info(f"  معدل النجاح: {(self.passed_tests/self.total_tests)*100:.1f}%")
        
        if self.failed_tests > 0:
            logger.info("\n❌ الاختبارات الفاشلة:")
            for test_name, result in self.test_results.items():
                if not result['success']:
                    logger.info(f"  - {test_name}: {result['message']}")
        
        if self.failed_tests == 0:
            logger.info("\n🎉 جميع الاختبارات نجحت! البوت جاهز للعمل.")
        else:
            logger.info(f"\n⚠️ {self.failed_tests} اختبار فشل. يرجى مراجعة المشاكل أعلاه.")

def main():
    """الدالة الرئيسية"""
    tester = BotTester()
    tester.run_all_tests()

if __name__ == "__main__":
    main()
