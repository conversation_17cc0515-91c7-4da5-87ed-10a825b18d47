-- ===================================================================
-- ملف SQL شامل لإنشاء جميع الجداول المفقودة
-- Comprehensive SQL file to create all missing tables
-- ===================================================================

-- تاريخ الإنشاء: 2025-08-04 20:25:45.525876
-- Creation date: 2025-08-04 20:25:45.525888

-- ===================================================================
-- جدول mods
-- Table mods
-- ===================================================================


                CREATE TABLE IF NOT EXISTS public.mods (
                    id SERIAL PRIMARY KEY,
                    name TEXT NOT NULL,
                    description TEXT,
                    description_ar TEXT,
                    telegram_description_ar TEXT,
                    telegram_description_en TEXT,
                    category TEXT NOT NULL CHECK (category IN ('addons', 'shaders', 'texture_packs', 'seeds', 'maps')),
                    version TEXT NOT NULL,
                    download_url TEXT NOT NULL,
                    image_urls TEXT[],
                    file_size TEXT,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    is_active BOOLEAN DEFAULT true,
                    download_count INTEGER DEFAULT 0,
                    rating DECIMAL(3,2) DEFAULT 0.00,
                    tags TEXT[],
                    author TEXT,
                    mod_type TEXT DEFAULT 'mod',
                    compatibility TEXT[],
                    requirements TEXT,
                    installation_guide TEXT
                );
                CREATE INDEX IF NOT EXISTS idx_mods_category ON public.mods(category);
                CREATE INDEX IF NOT EXISTS idx_mods_version ON public.mods(version);
                CREATE INDEX IF NOT EXISTS idx_mods_active ON public.mods(is_active);
                CREATE INDEX IF NOT EXISTS idx_mods_created_at ON public.mods(created_at);
                CREATE INDEX IF NOT EXISTS idx_mods_name ON public.mods(name);
            

-- ===================================================================
-- جدول saved_notifications
-- Table saved_notifications
-- ===================================================================


                CREATE TABLE IF NOT EXISTS public.saved_notifications (
                    id SERIAL PRIMARY KEY,
                    title TEXT NOT NULL,
                    message TEXT NOT NULL,
                    message_type TEXT DEFAULT 'text' CHECK (message_type IN ('text', 'photo', 'video', 'document')),
                    media_url TEXT,
                    button_text TEXT,
                    button_url TEXT,
                    created_by TEXT NOT NULL,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    is_active BOOLEAN DEFAULT true
                );
                CREATE INDEX IF NOT EXISTS idx_saved_notifications_created_by ON public.saved_notifications(created_by);
                CREATE INDEX IF NOT EXISTS idx_saved_notifications_active ON public.saved_notifications(is_active);
            

-- ===================================================================
-- جدول notification_broadcasts
-- Table notification_broadcasts
-- ===================================================================


                CREATE TABLE IF NOT EXISTS public.notification_broadcasts (
                    id SERIAL PRIMARY KEY,
                    notification_id INTEGER,
                    title TEXT NOT NULL,
                    message TEXT NOT NULL,
                    message_type TEXT DEFAULT 'text',
                    media_url TEXT,
                    button_text TEXT,
                    button_url TEXT,
                    sent_by TEXT NOT NULL,
                    total_users INTEGER DEFAULT 0,
                    successful_sends INTEGER DEFAULT 0,
                    failed_sends INTEGER DEFAULT 0,
                    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    completed_at TIMESTAMP WITH TIME ZONE,
                    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'sending', 'completed', 'failed')),
                    error_message TEXT
                );
                CREATE INDEX IF NOT EXISTS idx_notification_broadcasts_sent_by ON public.notification_broadcasts(sent_by);
                CREATE INDEX IF NOT EXISTS idx_notification_broadcasts_status ON public.notification_broadcasts(status);
            

-- ===================================================================
-- جدول notification_user_logs
-- Table notification_user_logs
-- ===================================================================


                CREATE TABLE IF NOT EXISTS public.notification_user_logs (
                    id SERIAL PRIMARY KEY,
                    broadcast_id INTEGER,
                    user_id TEXT NOT NULL,
                    sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'failed', 'blocked')),
                    error_message TEXT
                );
                CREATE INDEX IF NOT EXISTS idx_notification_user_logs_broadcast_id ON public.notification_user_logs(broadcast_id);
                CREATE INDEX IF NOT EXISTS idx_notification_user_logs_user_id ON public.notification_user_logs(user_id);
                CREATE INDEX IF NOT EXISTS idx_notification_user_logs_status ON public.notification_user_logs(status);
            

-- ===================================================================
-- جدول tasks
-- Table tasks
-- ===================================================================


                CREATE TABLE IF NOT EXISTS public.tasks (
                    id SERIAL PRIMARY KEY,
                    title TEXT NOT NULL,
                    description TEXT NOT NULL,
                    task_type TEXT NOT NULL CHECK (task_type IN ('join_channel', 'visit_link', 'share_content', 'custom')),
                    target_url TEXT,
                    channel_username TEXT,
                    reward_points INTEGER DEFAULT 0,
                    is_active BOOLEAN DEFAULT true,
                    created_by TEXT NOT NULL,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    expires_at TIMESTAMP WITH TIME ZONE,
                    max_completions INTEGER,
                    current_completions INTEGER DEFAULT 0
                );
                CREATE INDEX IF NOT EXISTS idx_tasks_active ON public.tasks(is_active);
                CREATE INDEX IF NOT EXISTS idx_tasks_type ON public.tasks(task_type);
            

-- ===================================================================
-- جدول user_task_completions
-- Table user_task_completions
-- ===================================================================


                CREATE TABLE IF NOT EXISTS public.user_task_completions (
                    id SERIAL PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    task_id INTEGER,
                    completed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    verified BOOLEAN DEFAULT false,
                    points_awarded INTEGER DEFAULT 0,
                    UNIQUE(user_id, task_id)
                );
                CREATE INDEX IF NOT EXISTS idx_user_task_completions_user_id ON public.user_task_completions(user_id);
                CREATE INDEX IF NOT EXISTS idx_user_task_completions_task_id ON public.user_task_completions(task_id);
            

-- ===================================================================
-- انتهى الملف
-- End of file
-- ===================================================================
