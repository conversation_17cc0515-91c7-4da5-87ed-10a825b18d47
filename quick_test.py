#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع للإصلاحات المطبقة
"""

import os
import sys
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

def test_database():
    """اختبار قاعدة البيانات"""
    print("🔍 اختبار قاعدة البيانات...")
    
    try:
        import requests
        
        USER_DB_URL = os.environ.get('USER_SUPABASE_URL', '')
        USER_DB_KEY = os.environ.get('USER_SUPABASE_KEY', '')
        
        if not USER_DB_URL or not USER_DB_KEY:
            print("❌ إعدادات قاعدة البيانات مفقودة")
            return False
        
        headers = {
            'apikey': USER_DB_KEY,
            'Authorization': f'Bearer {USER_DB_KEY}',
            'Content-Type': 'application/json'
        }
        
        url = f'{USER_DB_URL}/rest/v1/users'
        response = requests.get(url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            print("✅ قاعدة البيانات تعمل!")
            return True
        else:
            print(f"❌ خطأ: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def test_imports():
    """اختبار الاستيرادات"""
    print("🔍 اختبار الاستيرادات...")
    
    try:
        from user_database_client import test_user_database_connection
        print("✅ user_database_client: OK")
        
        # اختبار الاتصال
        result = test_user_database_connection()
        if result:
            print("✅ اختبار الاتصال: نجح")
        else:
            print("⚠️ اختبار الاتصال: فشل")
        
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 اختبار سريع للإصلاحات")
    print("=" * 40)
    
    tests = [
        ("قاعدة البيانات", test_database),
        ("الاستيرادات", test_imports),
    ]
    
    passed = 0
    
    for name, test_func in tests:
        print(f"\n📋 {name}")
        print("-" * 20)
        
        try:
            if test_func():
                print(f"✅ {name}: نجح")
                passed += 1
            else:
                print(f"❌ {name}: فشل")
        except Exception as e:
            print(f"💥 {name}: خطأ - {e}")
    
    print("\n" + "=" * 40)
    print(f"📊 النتيجة: {passed}/{len(tests)} نجح")
    
    if passed == len(tests):
        print("🎉 جميع الاختبارات نجحت!")
    else:
        print("⚠️ بعض الاختبارات فشلت")

if __name__ == "__main__":
    main()
