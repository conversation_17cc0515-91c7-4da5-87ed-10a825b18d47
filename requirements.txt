# متطلبات شاملة للبوت - Complete Bot Requirements
# جميع المكتبات المطلوبة لتشغيل البوت بدون أخطاء

# ===== المتطلبات الأساسية - Core Requirements =====
python-telegram-bot>=20.0
python-dotenv>=1.0.0
requests>=2.31.0
httpx>=0.24.0

# ===== خادم الويب - Web Server =====
flask>=2.3.0
flask-cors>=4.0.0
Jinja2>=3.1.0
Werkzeug>=2.3.0

# ===== قاعدة البيانات - Database =====
supabase>=1.0.0
postgrest>=0.10.0

# ===== الجدولة والمهام - Scheduling & Tasks =====
APScheduler>=3.10.0

# ===== الأمان والتشفير - Security & Encryption =====
cryptography>=41.0.0
bcrypt>=4.0.0
PyJWT>=2.8.0

# ===== معالجة الصور والملفات - Image & File Processing =====
Pillow>=10.0.0

# ===== الشبكة والاتصالات - Network & Communications =====
urllib3>=2.0.0
certifi>=2023.0.0
aiohttp>=3.8.0
websockets>=11.0.0

# ===== معالجة البيانات - Data Processing =====
python-dateutil>=2.8.0
pytz>=2023.3

# ===== أدوات إضافية - Additional Tools =====
psutil>=5.9.0
colorama>=0.4.6
