#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الإصلاحات المطبقة للاستضافة
Test applied hosting fixes
"""

import os
import sys
import time
import logging
import requests
from pathlib import Path

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_environment_setup():
    """اختبار إعداد البيئة"""
    logger.info("🔍 اختبار إعداد البيئة...")
    
    required_vars = [
        'BOT_TOKEN', 'TELEGRAM_BOT_TOKEN', 'SUPABASE_URL', 
        'SUPABASE_KEY', 'ADMIN_CHAT_ID'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
        else:
            logger.info(f"✅ {var}: موجود")
    
    if missing_vars:
        logger.error(f"❌ متغيرات مفقودة: {missing_vars}")
        return False
    
    logger.info("✅ جميع متغيرات البيئة موجودة")
    return True

def test_file_structure():
    """اختبار هيكل الملفات"""
    logger.info("🔍 اختبار هيكل الملفات...")
    
    required_files = [
        'single_instance_start.py',
        'instance_lock_manager.py',
        'database_connection_manager.py',
        'unified_startup.py',
        'Procfile',
        'render.yaml',
        'requirements.txt'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
        else:
            logger.info(f"✅ {file_path}: موجود")
    
    if missing_files:
        logger.error(f"❌ ملفات مفقودة: {missing_files}")
        return False
    
    logger.info("✅ جميع الملفات المطلوبة موجودة")
    return True

def test_procfile_content():
    """اختبار محتوى Procfile"""
    logger.info("🔍 اختبار محتوى Procfile...")
    
    try:
        with open('Procfile', 'r') as f:
            content = f.read().strip()
        
        if 'single_instance_start.py' in content:
            logger.info("✅ Procfile يستخدم single_instance_start.py")
            
            # تحقق من عدم وجود سطور متعددة تشغل البوت
            lines = [line.strip() for line in content.split('\n') if line.strip()]
            bot_lines = [line for line in lines if 'python' in line and '.py' in line]
            
            if len(bot_lines) == 1:
                logger.info("✅ Procfile يحتوي على سطر تشغيل واحد فقط")
                return True
            else:
                logger.error(f"❌ Procfile يحتوي على {len(bot_lines)} سطر تشغيل")
                return False
        else:
            logger.error("❌ Procfile لا يستخدم single_instance_start.py")
            return False
            
    except Exception as e:
        logger.error(f"❌ خطأ في قراءة Procfile: {e}")
        return False

def test_instance_lock_manager():
    """اختبار مدير قفل Instance"""
    logger.info("🔍 اختبار مدير قفل Instance...")
    
    try:
        from instance_lock_manager import InstanceLockManager
        
        # إنشاء مدير قفل للاختبار
        test_manager = InstanceLockManager("test_bot", "/tmp")
        
        # اختبار الحصول على القفل
        if test_manager.acquire_lock():
            logger.info("✅ تم الحصول على قفل الاختبار")
            
            # اختبار تحرير القفل
            test_manager.release_lock()
            logger.info("✅ تم تحرير قفل الاختبار")
            return True
        else:
            logger.error("❌ فشل في الحصول على قفل الاختبار")
            return False
            
    except ImportError as e:
        logger.error(f"❌ فشل في استيراد instance_lock_manager: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار مدير القفل: {e}")
        return False

def test_database_connection_manager():
    """اختبار مدير اتصال قاعدة البيانات"""
    logger.info("🔍 اختبار مدير اتصال قاعدة البيانات...")
    
    try:
        from database_connection_manager import (
            DatabaseConnectionManager, get_database_status
        )
        
        # اختبار إنشاء المدير
        db_manager = DatabaseConnectionManager()
        logger.info("✅ تم إنشاء مدير قاعدة البيانات")
        
        # اختبار الحصول على الحالة
        status = get_database_status()
        logger.info(f"✅ حالة قاعدة البيانات: {status}")
        
        return True
        
    except ImportError as e:
        logger.error(f"❌ فشل في استيراد database_connection_manager: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار مدير قاعدة البيانات: {e}")
        return False

def test_telegram_api_connection():
    """اختبار الاتصال مع Telegram API"""
    logger.info("🔍 اختبار الاتصال مع Telegram API...")
    
    bot_token = os.getenv('BOT_TOKEN')
    if not bot_token:
        logger.error("❌ BOT_TOKEN مفقود")
        return False
    
    try:
        # اختبار getMe
        url = f"https://api.telegram.org/bot{bot_token}/getMe"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('ok'):
                bot_info = result.get('result', {})
                bot_name = bot_info.get('first_name', 'Unknown')
                logger.info(f"✅ اتصال Telegram ناجح - البوت: {bot_name}")
                return True
            else:
                logger.error(f"❌ استجابة Telegram غير صحيحة: {result}")
                return False
        else:
            logger.error(f"❌ خطأ HTTP من Telegram: {response.status_code}")
            return False
            
    except requests.exceptions.Timeout:
        logger.error("❌ انتهت مهلة الاتصال مع Telegram")
        return False
    except Exception as e:
        logger.error(f"❌ خطأ في الاتصال مع Telegram: {e}")
        return False

def test_supabase_connection():
    """اختبار الاتصال مع Supabase"""
    logger.info("🔍 اختبار الاتصال مع Supabase...")
    
    supabase_url = os.getenv('SUPABASE_URL')
    supabase_key = os.getenv('SUPABASE_KEY')
    
    if not supabase_url or not supabase_key:
        logger.error("❌ إعدادات Supabase مفقودة")
        return False
    
    try:
        headers = {
            'apikey': supabase_key,
            'Authorization': f'Bearer {supabase_key}',
            'Content-Type': 'application/json'
        }
        
        url = f"{supabase_url.rstrip('/')}/rest/v1/"
        response = requests.get(url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            logger.info("✅ اتصال Supabase ناجح")
            return True
        elif response.status_code == 401:
            logger.error("❌ مفتاح Supabase غير صحيح")
            return False
        else:
            logger.error(f"❌ خطأ HTTP من Supabase: {response.status_code}")
            return False
            
    except requests.exceptions.Timeout:
        logger.error("❌ انتهت مهلة الاتصال مع Supabase")
        return False
    except Exception as e:
        logger.error(f"❌ خطأ في الاتصال مع Supabase: {e}")
        return False

def run_all_tests():
    """تشغيل جميع الاختبارات"""
    logger.info("🚀 بدء اختبار الإصلاحات المطبقة")
    logger.info("=" * 50)
    
    tests = [
        ("إعداد البيئة", test_environment_setup),
        ("هيكل الملفات", test_file_structure),
        ("محتوى Procfile", test_procfile_content),
        ("مدير قفل Instance", test_instance_lock_manager),
        ("مدير قاعدة البيانات", test_database_connection_manager),
        ("اتصال Telegram", test_telegram_api_connection),
        ("اتصال Supabase", test_supabase_connection)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n🔄 اختبار: {test_name}")
        try:
            if test_func():
                passed_tests += 1
                logger.info(f"✅ {test_name}: نجح")
            else:
                logger.error(f"❌ {test_name}: فشل")
        except Exception as e:
            logger.error(f"❌ {test_name}: خطأ غير متوقع - {e}")
    
    logger.info("\n" + "=" * 50)
    logger.info(f"📊 نتائج الاختبارات: {passed_tests}/{total_tests} نجح")
    
    if passed_tests == total_tests:
        logger.info("🎉 جميع الاختبارات نجحت! البوت جاهز للنشر")
        return True
    else:
        logger.warning(f"⚠️ {total_tests - passed_tests} اختبار فشل. راجع المشاكل أعلاه")
        return False

if __name__ == "__main__":
    # تعيين متغيرات البيئة للاختبار إذا لم تكن موجودة
    env_vars = {
        'BOT_TOKEN': '**********************************************',
        'TELEGRAM_BOT_TOKEN': '**********************************************',
        'SUPABASE_URL': 'https://ytqxxodyecdeosnqoure.supabase.co',
        'SUPABASE_KEY': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4',
        'ADMIN_CHAT_ID': '7513880877'
    }
    
    for key, value in env_vars.items():
        if not os.getenv(key):
            os.environ[key] = value
    
    # تشغيل الاختبارات
    success = run_all_tests()
    sys.exit(0 if success else 1)
