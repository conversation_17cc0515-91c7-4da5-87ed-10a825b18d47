-- SQL لإنشاء الجداول المفقودة
-- تاريخ الإنشاء: 2025-08-04 20:25:45.524033


                CREATE TABLE IF NOT EXISTS public.user_task_completions (
                    id SERIAL PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    task_id INTEGER,
                    completed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    verified BOOLEAN DEFAULT false,
                    points_awarded INTEGER DEFAULT 0,
                    UNIQUE(user_id, task_id)
                );
                CREATE INDEX IF NOT EXISTS idx_user_task_completions_user_id ON public.user_task_completions(user_id);
                CREATE INDEX IF NOT EXISTS idx_user_task_completions_task_id ON public.user_task_completions(task_id);
            