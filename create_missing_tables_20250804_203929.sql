-- SQL لإنشاء الجداول المفقودة
-- تاريخ الإنشاء: 2025-08-04 20:39:29.792875


                CREATE TABLE IF NOT EXISTS public.notification_broadcasts (
                    id SERIAL PRIMARY KEY,
                    notification_id INTEGER,
                    title TEXT NOT NULL,
                    message TEXT NOT NULL,
                    message_type TEXT DEFAULT 'text',
                    media_url TEXT,
                    button_text TEXT,
                    button_url TEXT,
                    sent_by TEXT NOT NULL,
                    total_users INTEGER DEFAULT 0,
                    successful_sends INTEGER DEFAULT 0,
                    failed_sends INTEGER DEFAULT 0,
                    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    completed_at TIMESTAMP WITH TIME ZONE,
                    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'sending', 'completed', 'failed')),
                    error_message TEXT
                );
                CREATE INDEX IF NOT EXISTS idx_notification_broadcasts_sent_by ON public.notification_broadcasts(sent_by);
                CREATE INDEX IF NOT EXISTS idx_notification_broadcasts_status ON public.notification_broadcasts(status);
            