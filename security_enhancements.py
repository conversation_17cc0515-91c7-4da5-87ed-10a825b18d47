"""
تحسينات أمنية إضافية للبوت
Additional Security Enhancements for the Bot
"""

import os
import logging
import hashlib
import secrets
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import ipaddress
import re

# إعداد التسجيل الأمني
security_logger = logging.getLogger('security_enhancements')

class AdvancedSecurityManager:
    """مدير الحماية المتقدم"""
    
    def __init__(self):
        self.failed_attempts = {}
        self.suspicious_ips = set()
        self.blocked_users = set()
        self.session_tokens = {}
        self.api_rate_limits = {}
        
        # تحميل البيانات المحفوظة
        self.load_security_data()
    
    def load_security_data(self):
        """تحميل بيانات الحماية من الملفات"""
        try:
            if os.path.exists('security_data.json'):
                with open('security_data.json', 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.blocked_users = set(data.get('blocked_users', []))
                    self.suspicious_ips = set(data.get('suspicious_ips', []))
                    security_logger.info("تم تحميل بيانات الحماية")
        except Exception as e:
            security_logger.error(f"فشل في تحميل بيانات الحماية: {e}")
    
    def save_security_data(self):
        """حفظ بيانات الحماية"""
        try:
            data = {
                'blocked_users': list(self.blocked_users),
                'suspicious_ips': list(self.suspicious_ips),
                'last_updated': datetime.now().isoformat()
            }
            with open('security_data.json', 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            security_logger.error(f"فشل في حفظ بيانات الحماية: {e}")
    
    def generate_secure_token(self, user_id: str) -> str:
        """توليد رمز آمن للمستخدم"""
        timestamp = str(int(time.time()))
        random_data = secrets.token_hex(16)
        data = f"{user_id}:{timestamp}:{random_data}"
        token = hashlib.sha256(data.encode()).hexdigest()
        
        # حفظ الرمز مع انتهاء صلاحية
        expiry = datetime.now() + timedelta(hours=24)
        self.session_tokens[token] = {
            'user_id': user_id,
            'created': datetime.now().isoformat(),
            'expires': expiry.isoformat()
        }
        
        return token
    
    def validate_token(self, token: str, user_id: str) -> bool:
        """التحقق من صحة الرمز"""
        if token not in self.session_tokens:
            return False
        
        token_data = self.session_tokens[token]
        
        # فحص انتهاء الصلاحية
        expiry = datetime.fromisoformat(token_data['expires'])
        if datetime.now() > expiry:
            del self.session_tokens[token]
            return False
        
        # فحص المستخدم
        return token_data['user_id'] == user_id
    
    def log_failed_attempt(self, user_id: str, attempt_type: str, ip_address: str = None):
        """تسجيل محاولة فاشلة"""
        current_time = datetime.now()
        
        if user_id not in self.failed_attempts:
            self.failed_attempts[user_id] = []
        
        self.failed_attempts[user_id].append({
            'type': attempt_type,
            'timestamp': current_time.isoformat(),
            'ip': ip_address
        })
        
        # تنظيف المحاولات القديمة (أكثر من ساعة)
        cutoff_time = current_time - timedelta(hours=1)
        self.failed_attempts[user_id] = [
            attempt for attempt in self.failed_attempts[user_id]
            if datetime.fromisoformat(attempt['timestamp']) > cutoff_time
        ]
        
        # فحص إذا تجاوز الحد المسموح
        if len(self.failed_attempts[user_id]) >= 5:
            self.block_user(user_id, f"Multiple failed attempts: {attempt_type}")
            if ip_address:
                self.suspicious_ips.add(ip_address)
        
        security_logger.warning(f"Failed attempt: {attempt_type} from user {user_id} (IP: {ip_address})")
    
    def block_user(self, user_id: str, reason: str):
        """حظر مستخدم"""
        self.blocked_users.add(user_id)
        self.save_security_data()
        security_logger.error(f"User {user_id} blocked: {reason}")
    
    def unblock_user(self, user_id: str):
        """إلغاء حظر مستخدم"""
        self.blocked_users.discard(user_id)
        if user_id in self.failed_attempts:
            del self.failed_attempts[user_id]
        self.save_security_data()
        security_logger.info(f"User {user_id} unblocked")
    
    def is_user_blocked(self, user_id: str) -> bool:
        """فحص ما إذا كان المستخدم محظور"""
        return user_id in self.blocked_users
    
    def check_rate_limit(self, user_id: str, action: str, limit: int = 10, window: int = 60) -> bool:
        """فحص معدل الطلبات"""
        current_time = time.time()
        key = f"{user_id}:{action}"
        
        if key not in self.api_rate_limits:
            self.api_rate_limits[key] = []
        
        # تنظيف الطلبات القديمة
        self.api_rate_limits[key] = [
            timestamp for timestamp in self.api_rate_limits[key]
            if current_time - timestamp < window
        ]
        
        # فحص الحد
        if len(self.api_rate_limits[key]) >= limit:
            self.log_failed_attempt(user_id, f"rate_limit_exceeded_{action}")
            return False
        
        # إضافة الطلب الحالي
        self.api_rate_limits[key].append(current_time)
        return True

class DataSanitizer:
    """منظف البيانات"""
    
    @staticmethod
    def sanitize_filename(filename: str) -> str:
        """تنظيف اسم الملف"""
        # إزالة الأحرف الخطيرة
        filename = re.sub(r'[<>:"/\\|?*]', '', filename)
        # تحديد الطول
        filename = filename[:255]
        # إزالة المسارات النسبية
        filename = os.path.basename(filename)
        return filename
    
    @staticmethod
    def sanitize_url(url: str) -> Optional[str]:
        """تنظيف والتحقق من الرابط"""
        if not url or not isinstance(url, str):
            return None
        
        # إزالة المسافات
        url = url.strip()
        
        # فحص البروتوكول
        if not url.startswith(('http://', 'https://')):
            return None
        
        # فحص الطول
        if len(url) > 2048:
            return None
        
        # فحص الأحرف الخطيرة
        dangerous_patterns = [
            'javascript:', 'data:', 'vbscript:', 'file:',
            '<script', '</script>', 'eval(', 'exec('
        ]
        
        url_lower = url.lower()
        for pattern in dangerous_patterns:
            if pattern in url_lower:
                return None
        
        return url
    
    @staticmethod
    def sanitize_text(text: str, max_length: int = 4096) -> str:
        """تنظيف النص"""
        if not text or not isinstance(text, str):
            return ""
        
        # إزالة الأحرف الخطيرة
        text = re.sub(r'<script[^>]*>.*?</script>', '', text, flags=re.IGNORECASE | re.DOTALL)
        text = re.sub(r'javascript:', '', text, flags=re.IGNORECASE)
        text = re.sub(r'on\w+\s*=', '', text, flags=re.IGNORECASE)
        
        # تحديد الطول
        if len(text) > max_length:
            text = text[:max_length]
        
        return text.strip()

class EncryptionManager:
    """مدير التشفير"""
    
    def __init__(self, key: str = None):
        self.key = key or os.environ.get('ENCRYPTION_KEY', 'default_key')
    
    def encrypt_sensitive_data(self, data: str) -> str:
        """تشفير البيانات الحساسة"""
        try:
            import base64
            import hmac
            
            # إنشاء salt عشوائي
            salt = secrets.token_bytes(16)
            
            # إنشاء مفتاح مشتق
            derived_key = hashlib.pbkdf2_hmac('sha256', self.key.encode(), salt, 100000)
            
            # تشفير البيانات (تشفير بسيط للمثال)
            encrypted = base64.b64encode(data.encode()).decode()
            
            # إنشاء HMAC للتحقق من التكامل
            mac = hmac.new(derived_key, encrypted.encode(), hashlib.sha256).hexdigest()
            
            # دمج البيانات
            result = base64.b64encode(salt).decode() + ':' + encrypted + ':' + mac
            return result
            
        except Exception as e:
            security_logger.error(f"فشل في تشفير البيانات: {e}")
            return data
    
    def decrypt_sensitive_data(self, encrypted_data: str) -> Optional[str]:
        """فك تشفير البيانات الحساسة"""
        try:
            import base64
            import hmac
            
            # فصل المكونات
            parts = encrypted_data.split(':')
            if len(parts) != 3:
                return None
            
            salt = base64.b64decode(parts[0])
            encrypted = parts[1]
            mac = parts[2]
            
            # إنشاء مفتاح مشتق
            derived_key = hashlib.pbkdf2_hmac('sha256', self.key.encode(), salt, 100000)
            
            # التحقق من HMAC
            expected_mac = hmac.new(derived_key, encrypted.encode(), hashlib.sha256).hexdigest()
            if not hmac.compare_digest(mac, expected_mac):
                return None
            
            # فك التشفير
            decrypted = base64.b64decode(encrypted).decode()
            return decrypted
            
        except Exception as e:
            security_logger.error(f"فشل في فك تشفير البيانات: {e}")
            return None

# إنشاء كائنات النظام
security_manager = AdvancedSecurityManager()
data_sanitizer = DataSanitizer()
encryption_manager = EncryptionManager()

def enhanced_security_check(func):
    """ديكوريتر للفحص الأمني المحسن"""
    def wrapper(update, context, *args, **kwargs):
        user_id = str(update.effective_user.id) if update.effective_user else "unknown"
        
        # فحص الحظر
        if security_manager.is_user_blocked(user_id):
            security_logger.warning(f"Blocked user {user_id} attempted access")
            return
        
        # فحص معدل الطلبات
        if not security_manager.check_rate_limit(user_id, func.__name__):
            security_logger.warning(f"Rate limit exceeded for user {user_id}")
            return
        
        try:
            return func(update, context, *args, **kwargs)
        except Exception as e:
            security_logger.error(f"Error in {func.__name__} for user {user_id}: {e}")
            security_manager.log_failed_attempt(user_id, f"function_error_{func.__name__}")
            raise
    
    return wrapper

def log_security_event(event_type: str, user_id: str, details: str = ""):
    """تسجيل حدث أمني"""
    security_logger.info(f"Security Event: {event_type} | User: {user_id} | Details: {details}")

def initialize_enhanced_security():
    """تهيئة النظام الأمني المحسن"""
    try:
        # إنشاء مجلدات الأمان
        os.makedirs('security_logs', exist_ok=True)
        
        # إعداد معالج السجلات الأمنية
        security_handler = logging.FileHandler('security_logs/security_enhanced.log', encoding='utf-8')
        security_formatter = logging.Formatter(
            '%(asctime)s - SECURITY_ENHANCED - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        security_handler.setFormatter(security_formatter)
        security_logger.addHandler(security_handler)
        security_logger.setLevel(logging.INFO)
        
        security_logger.info("Enhanced security system initialized")
        return True
        
    except Exception as e:
        print(f"Failed to initialize enhanced security: {e}")
        return False

# تشغيل التهيئة
initialize_enhanced_security()
