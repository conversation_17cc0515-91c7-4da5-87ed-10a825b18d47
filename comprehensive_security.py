"""
نظام الحماية الشامل للبوت
Comprehensive Security System for the Bot
"""

import os
import logging
import hashlib
import secrets
import time
import json
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from functools import wraps

# إعداد التسجيل
logger = logging.getLogger(__name__)

class ComprehensiveSecuritySystem:
    """نظام الحماية الشامل"""
    
    def __init__(self):
        self.config = self._load_security_config()
        self.threat_detector = ThreatDetector()
        self.access_controller = AccessController()
        self.data_protector = DataProtector()
        self.audit_logger = AuditLogger()
        
        # تهيئة النظام
        self._initialize_security()
    
    def _load_security_config(self) -> Dict[str, Any]:
        """تحميل إعدادات الحماية"""
        default_config = {
            'max_login_attempts': 5,
            'lockout_duration': 300,  # 5 دقائق
            'session_timeout': 3600,  # ساعة واحدة
            'rate_limits': {
                'messages': {'limit': 30, 'window': 60},
                'commands': {'limit': 10, 'window': 60},
                'api_calls': {'limit': 100, 'window': 60}
            },
            'blocked_patterns': [
                r'<script[^>]*>.*?</script>',
                r'javascript:',
                r'on\w+\s*=',
                r'eval\s*\(',
                r'exec\s*\(',
                r'union\s+select',
                r'drop\s+table',
                r'delete\s+from'
            ],
            'allowed_file_types': ['.jpg', '.jpeg', '.png', '.gif', '.pdf', '.zip'],
            'max_file_size': 10 * 1024 * 1024,  # 10 MB
            'encryption_enabled': True,
            'audit_enabled': True
        }
        
        try:
            if os.path.exists('security_config.json'):
                with open('security_config.json', 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                    default_config.update(user_config)
        except Exception as e:
            logger.warning(f"فشل في تحميل إعدادات الحماية: {e}")
        
        return default_config
    
    def _initialize_security(self):
        """تهيئة نظام الحماية"""
        try:
            # إنشاء مجلدات الأمان
            os.makedirs('security', exist_ok=True)
            os.makedirs('security/logs', exist_ok=True)
            os.makedirs('security/quarantine', exist_ok=True)
            
            # تهيئة المكونات
            self.threat_detector.initialize()
            self.access_controller.initialize()
            self.data_protector.initialize()
            self.audit_logger.initialize()
            
            logger.info("✅ تم تهيئة نظام الحماية الشامل")
            
        except Exception as e:
            logger.error(f"❌ فشل في تهيئة نظام الحماية: {e}")
    
    def validate_request(self, user_id: str, request_type: str, data: Any = None) -> bool:
        """التحقق من صحة الطلب"""
        try:
            # فحص التحكم في الوصول
            if not self.access_controller.check_access(user_id, request_type):
                return False
            
            # فحص كشف التهديدات
            if not self.threat_detector.scan_request(user_id, request_type, data):
                return False
            
            # تسجيل الطلب
            self.audit_logger.log_request(user_id, request_type, 'allowed')
            
            return True
            
        except Exception as e:
            logger.error(f"خطأ في التحقق من الطلب: {e}")
            return False
    
    def protect_data(self, data: str, user_id: str = None) -> str:
        """حماية البيانات"""
        return self.data_protector.protect(data, user_id)
    
    def unprotect_data(self, protected_data: str, user_id: str = None) -> Optional[str]:
        """إلغاء حماية البيانات"""
        return self.data_protector.unprotect(protected_data, user_id)

class ThreatDetector:
    """كاشف التهديدات"""
    
    def __init__(self):
        self.suspicious_patterns = []
        self.threat_scores = {}
        self.blocked_ips = set()
        self.malicious_signatures = []
    
    def initialize(self):
        """تهيئة كاشف التهديدات"""
        self._load_threat_database()
        self._load_blocked_ips()
    
    def _load_threat_database(self):
        """تحميل قاعدة بيانات التهديدات"""
        try:
            if os.path.exists('security/threats.json'):
                with open('security/threats.json', 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.suspicious_patterns = data.get('patterns', [])
                    self.malicious_signatures = data.get('signatures', [])
        except Exception as e:
            logger.warning(f"فشل في تحميل قاعدة بيانات التهديدات: {e}")
    
    def _load_blocked_ips(self):
        """تحميل قائمة عناوين IP المحظورة"""
        try:
            if os.path.exists('security/blocked_ips.json'):
                with open('security/blocked_ips.json', 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.blocked_ips = set(data.get('ips', []))
        except Exception as e:
            logger.warning(f"فشل في تحميل قائمة IP المحظورة: {e}")
    
    def scan_request(self, user_id: str, request_type: str, data: Any = None) -> bool:
        """فحص الطلب للتهديدات"""
        threat_score = 0
        
        # فحص الأنماط المشبوهة
        if data and isinstance(data, str):
            for pattern in self.suspicious_patterns:
                if re.search(pattern, data, re.IGNORECASE):
                    threat_score += 10
        
        # فحص التوقيعات الضارة
        if data:
            data_str = str(data)
            for signature in self.malicious_signatures:
                if signature in data_str:
                    threat_score += 20
        
        # فحص معدل الطلبات
        current_time = time.time()
        if user_id not in self.threat_scores:
            self.threat_scores[user_id] = []
        
        # تنظيف النقاط القديمة
        self.threat_scores[user_id] = [
            score for score in self.threat_scores[user_id]
            if current_time - score['timestamp'] < 300  # 5 دقائق
        ]
        
        # إضافة النقاط الحالية
        self.threat_scores[user_id].append({
            'score': threat_score,
            'timestamp': current_time,
            'type': request_type
        })
        
        # حساب النقاط الإجمالية
        total_score = sum(score['score'] for score in self.threat_scores[user_id])
        
        # تحديد ما إذا كان الطلب مشبوه
        if total_score > 50:
            logger.warning(f"طلب مشبوه من المستخدم {user_id}: نقاط التهديد = {total_score}")
            return False
        
        return True

class AccessController:
    """متحكم الوصول"""
    
    def __init__(self):
        self.failed_attempts = {}
        self.locked_users = {}
        self.rate_limits = {}
        self.permissions = {}
    
    def initialize(self):
        """تهيئة متحكم الوصول"""
        self._load_permissions()
        self._load_locked_users()
    
    def _load_permissions(self):
        """تحميل الصلاحيات"""
        try:
            if os.path.exists('security/permissions.json'):
                with open('security/permissions.json', 'r', encoding='utf-8') as f:
                    self.permissions = json.load(f)
        except Exception as e:
            logger.warning(f"فشل في تحميل الصلاحيات: {e}")
    
    def _load_locked_users(self):
        """تحميل المستخدمين المحظورين"""
        try:
            if os.path.exists('security/locked_users.json'):
                with open('security/locked_users.json', 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.locked_users = {
                        user_id: datetime.fromisoformat(timestamp)
                        for user_id, timestamp in data.items()
                    }
        except Exception as e:
            logger.warning(f"فشل في تحميل المستخدمين المحظورين: {e}")
    
    def check_access(self, user_id: str, action: str) -> bool:
        """فحص الوصول"""
        # فحص الحظر
        if self._is_user_locked(user_id):
            return False
        
        # فحص معدل الطلبات
        if not self._check_rate_limit(user_id, action):
            return False
        
        # فحص الصلاحيات
        if not self._check_permissions(user_id, action):
            return False
        
        return True
    
    def _is_user_locked(self, user_id: str) -> bool:
        """فحص ما إذا كان المستخدم محظور"""
        if user_id in self.locked_users:
            lock_time = self.locked_users[user_id]
            if datetime.now() < lock_time:
                return True
            else:
                del self.locked_users[user_id]
                self._save_locked_users()
        return False
    
    def _check_rate_limit(self, user_id: str, action: str) -> bool:
        """فحص معدل الطلبات"""
        current_time = time.time()
        key = f"{user_id}:{action}"
        
        if key not in self.rate_limits:
            self.rate_limits[key] = []
        
        # تنظيف الطلبات القديمة
        self.rate_limits[key] = [
            timestamp for timestamp in self.rate_limits[key]
            if current_time - timestamp < 60  # نافزة دقيقة واحدة
        ]
        
        # فحص الحد
        if len(self.rate_limits[key]) >= 30:  # 30 طلب في الدقيقة
            self._record_failed_attempt(user_id, 'rate_limit_exceeded')
            return False
        
        # إضافة الطلب الحالي
        self.rate_limits[key].append(current_time)
        return True
    
    def _check_permissions(self, user_id: str, action: str) -> bool:
        """فحص الصلاحيات"""
        user_permissions = self.permissions.get(user_id, [])
        return action in user_permissions or 'all' in user_permissions
    
    def _record_failed_attempt(self, user_id: str, reason: str):
        """تسجيل محاولة فاشلة"""
        current_time = datetime.now()
        
        if user_id not in self.failed_attempts:
            self.failed_attempts[user_id] = []
        
        self.failed_attempts[user_id].append({
            'timestamp': current_time.isoformat(),
            'reason': reason
        })
        
        # تنظيف المحاولات القديمة
        cutoff_time = current_time - timedelta(hours=1)
        self.failed_attempts[user_id] = [
            attempt for attempt in self.failed_attempts[user_id]
            if datetime.fromisoformat(attempt['timestamp']) > cutoff_time
        ]
        
        # فحص إذا تجاوز الحد
        if len(self.failed_attempts[user_id]) >= 5:
            self._lock_user(user_id)
    
    def _lock_user(self, user_id: str):
        """حظر مستخدم"""
        lock_until = datetime.now() + timedelta(minutes=5)
        self.locked_users[user_id] = lock_until
        self._save_locked_users()
        logger.warning(f"تم حظر المستخدم {user_id} حتى {lock_until}")
    
    def _save_locked_users(self):
        """حفظ المستخدمين المحظورين"""
        try:
            data = {
                user_id: timestamp.isoformat()
                for user_id, timestamp in self.locked_users.items()
            }
            with open('security/locked_users.json', 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"فشل في حفظ المستخدمين المحظورين: {e}")

class DataProtector:
    """حامي البيانات"""
    
    def __init__(self):
        self.encryption_key = self._get_encryption_key()
    
    def initialize(self):
        """تهيئة حامي البيانات"""
        pass
    
    def _get_encryption_key(self) -> str:
        """الحصول على مفتاح التشفير"""
        key = os.environ.get('DATA_ENCRYPTION_KEY')
        if not key:
            key = hashlib.sha256(f"default_key_{secrets.token_hex(16)}".encode()).hexdigest()
        return key
    
    def protect(self, data: str, user_id: str = None) -> str:
        """حماية البيانات"""
        try:
            # تنظيف البيانات
            cleaned_data = self._sanitize_data(data)
            
            # تشفير البيانات إذا كان مطلوب
            if self._should_encrypt(data):
                encrypted_data = self._encrypt_data(cleaned_data)
                return encrypted_data
            
            return cleaned_data
            
        except Exception as e:
            logger.error(f"فشل في حماية البيانات: {e}")
            return data
    
    def unprotect(self, protected_data: str, user_id: str = None) -> Optional[str]:
        """إلغاء حماية البيانات"""
        try:
            # محاولة فك التشفير
            if self._is_encrypted(protected_data):
                return self._decrypt_data(protected_data)
            
            return protected_data
            
        except Exception as e:
            logger.error(f"فشل في إلغاء حماية البيانات: {e}")
            return None
    
    def _sanitize_data(self, data: str) -> str:
        """تنظيف البيانات"""
        # إزالة الأحرف الخطيرة
        dangerous_patterns = [
            r'<script[^>]*>.*?</script>',
            r'javascript:',
            r'on\w+\s*=',
            r'eval\s*\(',
            r'exec\s*\('
        ]
        
        cleaned = data
        for pattern in dangerous_patterns:
            cleaned = re.sub(pattern, '', cleaned, flags=re.IGNORECASE | re.DOTALL)
        
        return cleaned.strip()
    
    def _should_encrypt(self, data: str) -> bool:
        """تحديد ما إذا كان يجب تشفير البيانات"""
        # تشفير البيانات الحساسة
        sensitive_keywords = ['password', 'token', 'key', 'secret', 'private']
        data_lower = data.lower()
        return any(keyword in data_lower for keyword in sensitive_keywords)
    
    def _encrypt_data(self, data: str) -> str:
        """تشفير البيانات"""
        import base64
        # تشفير بسيط للمثال
        encoded = base64.b64encode(data.encode()).decode()
        return f"ENC:{encoded}"
    
    def _decrypt_data(self, encrypted_data: str) -> str:
        """فك تشفير البيانات"""
        import base64
        if encrypted_data.startswith("ENC:"):
            encoded = encrypted_data[4:]
            return base64.b64decode(encoded).decode()
        return encrypted_data
    
    def _is_encrypted(self, data: str) -> bool:
        """فحص ما إذا كانت البيانات مشفرة"""
        return data.startswith("ENC:")

class AuditLogger:
    """مسجل التدقيق"""
    
    def __init__(self):
        self.log_file = 'security/logs/audit.log'
    
    def initialize(self):
        """تهيئة مسجل التدقيق"""
        os.makedirs(os.path.dirname(self.log_file), exist_ok=True)
    
    def log_request(self, user_id: str, action: str, result: str, details: str = ""):
        """تسجيل طلب"""
        timestamp = datetime.now().isoformat()
        log_entry = {
            'timestamp': timestamp,
            'user_id': user_id,
            'action': action,
            'result': result,
            'details': details
        }
        
        try:
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(log_entry, ensure_ascii=False) + '\n')
        except Exception as e:
            logger.error(f"فشل في تسجيل التدقيق: {e}")

# إنشاء النظام الشامل
comprehensive_security = ComprehensiveSecuritySystem()

def secure_endpoint(action: str = "default"):
    """ديكوريتر لحماية نقاط النهاية"""
    def decorator(func):
        @wraps(func)
        def wrapper(update, context, *args, **kwargs):
            user_id = str(update.effective_user.id) if update.effective_user else "unknown"
            
            # التحقق من الأمان
            if not comprehensive_security.validate_request(user_id, action):
                logger.warning(f"طلب مرفوض من المستخدم {user_id} للإجراء {action}")
                return
            
            try:
                return func(update, context, *args, **kwargs)
            except Exception as e:
                comprehensive_security.audit_logger.log_request(
                    user_id, action, 'error', str(e)
                )
                raise
        
        return wrapper
    return decorator
