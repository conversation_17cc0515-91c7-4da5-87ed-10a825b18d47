#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة النشر التلقائي للبوت
Automatic Bot Deployment Tool

هذا الملف يقوم بنشر البوت على منصات الاستضافة المختلفة
This file deploys the bot to various hosting platforms
"""

import os
import sys
import subprocess
import logging
import json
from pathlib import Path
from typing import Dict, List, Optional

# إعداد التسجيل
logging.basicConfig(
    format="%(asctime)s - %(levelname)s - %(message)s",
    level=logging.INFO,
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger(__name__)

class BotDeployer:
    """فئة نشر البوت"""
    
    def __init__(self):
        self.project_root = Path.cwd()
        self.deployment_configs = {}
        
    def check_prerequisites(self) -> bool:
        """فحص المتطلبات الأساسية"""
        logger.info("🔍 فحص المتطلبات الأساسية...")
        
        required_files = [
            "main.py",
            "requirements.txt",
            ".env"
        ]
        
        missing_files = []
        for file_name in required_files:
            if not Path(file_name).exists():
                missing_files.append(file_name)
        
        if missing_files:
            logger.error(f"❌ ملفات مفقودة: {missing_files}")
            return False
        
        logger.info("✅ جميع الملفات المطلوبة موجودة")
        return True
    
    def create_procfile(self) -> bool:
        """إنشاء ملف Procfile لـ Heroku"""
        logger.info("📝 إنشاء ملف Procfile...")
        
        procfile_content = "worker: python main.py\n"
        
        try:
            with open("Procfile", 'w', encoding='utf-8') as f:
                f.write(procfile_content)
            
            logger.info("✅ تم إنشاء ملف Procfile")
            return True
            
        except Exception as e:
            logger.error(f"❌ فشل في إنشاء Procfile: {e}")
            return False
    
    def create_runtime_txt(self) -> bool:
        """إنشاء ملف runtime.txt"""
        logger.info("📝 إنشاء ملف runtime.txt...")
        
        python_version = f"python-{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
        
        try:
            with open("runtime.txt", 'w', encoding='utf-8') as f:
                f.write(f"{python_version}\n")
            
            logger.info(f"✅ تم إنشاء runtime.txt مع {python_version}")
            return True
            
        except Exception as e:
            logger.error(f"❌ فشل في إنشاء runtime.txt: {e}")
            return False
    
    def create_app_json(self) -> bool:
        """إنشاء ملف app.json لـ Heroku"""
        logger.info("📝 إنشاء ملف app.json...")
        
        app_config = {
            "name": "telegram-bot",
            "description": "Telegram Bot with Web Interface",
            "keywords": ["telegram", "bot", "python"],
            "website": "https://github.com/your-username/your-repo",
            "repository": "https://github.com/your-username/your-repo",
            "logo": "https://telegram.org/img/t_logo.png",
            "success_url": "/",
            "env": {
                "BOT_TOKEN": {
                    "description": "Telegram Bot Token",
                    "required": True
                },
                "ADMIN_CHAT_ID": {
                    "description": "Admin Chat ID",
                    "required": True
                },
                "SUPABASE_URL": {
                    "description": "Supabase Project URL",
                    "required": True
                },
                "SUPABASE_KEY": {
                    "description": "Supabase API Key",
                    "required": True
                },
                "ENVIRONMENT": {
                    "description": "Environment",
                    "value": "production"
                },
                "DEBUG": {
                    "description": "Debug Mode",
                    "value": "false"
                }
            },
            "formation": {
                "worker": {
                    "quantity": 1,
                    "size": "eco"
                }
            },
            "buildpacks": [
                {
                    "url": "heroku/python"
                }
            ]
        }
        
        try:
            with open("app.json", 'w', encoding='utf-8') as f:
                json.dump(app_config, f, indent=2, ensure_ascii=False)
            
            logger.info("✅ تم إنشاء ملف app.json")
            return True
            
        except Exception as e:
            logger.error(f"❌ فشل في إنشاء app.json: {e}")
            return False
    
    def create_render_yaml(self) -> bool:
        """إنشاء ملف render.yaml لـ Render"""
        logger.info("📝 إنشاء ملف render.yaml...")
        
        render_config = """services:
  - type: web
    name: telegram-bot
    env: python
    buildCommand: pip install -r requirements.txt
    startCommand: python main.py
    envVars:
      - key: BOT_TOKEN
        sync: false
      - key: ADMIN_CHAT_ID
        sync: false
      - key: SUPABASE_URL
        sync: false
      - key: SUPABASE_KEY
        sync: false
      - key: ENVIRONMENT
        value: production
      - key: DEBUG
        value: false
"""
        
        try:
            with open("render.yaml", 'w', encoding='utf-8') as f:
                f.write(render_config)
            
            logger.info("✅ تم إنشاء ملف render.yaml")
            return True
            
        except Exception as e:
            logger.error(f"❌ فشل في إنشاء render.yaml: {e}")
            return False
    
    def create_dockerfile(self) -> bool:
        """إنشاء ملف Dockerfile"""
        logger.info("📝 إنشاء ملف Dockerfile...")
        
        dockerfile_content = f"""FROM python:{sys.version_info.major}.{sys.version_info.minor}-slim

# تعيين مجلد العمل
WORKDIR /app

# نسخ ملفات المتطلبات
COPY requirements.txt .

# تثبيت المتطلبات
RUN pip install --no-cache-dir -r requirements.txt

# نسخ ملفات التطبيق
COPY . .

# تعيين متغيرات البيئة
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# تشغيل التطبيق
CMD ["python", "main.py"]
"""
        
        try:
            with open("Dockerfile", 'w', encoding='utf-8') as f:
                f.write(dockerfile_content)
            
            logger.info("✅ تم إنشاء ملف Dockerfile")
            return True
            
        except Exception as e:
            logger.error(f"❌ فشل في إنشاء Dockerfile: {e}")
            return False
    
    def create_docker_compose(self) -> bool:
        """إنشاء ملف docker-compose.yml"""
        logger.info("📝 إنشاء ملف docker-compose.yml...")
        
        compose_content = """version: '3.8'

services:
  telegram-bot:
    build: .
    environment:
      - BOT_TOKEN=${BOT_TOKEN}
      - ADMIN_CHAT_ID=${ADMIN_CHAT_ID}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - ENVIRONMENT=production
      - DEBUG=false
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
"""
        
        try:
            with open("docker-compose.yml", 'w', encoding='utf-8') as f:
                f.write(compose_content)
            
            logger.info("✅ تم إنشاء ملف docker-compose.yml")
            return True
            
        except Exception as e:
            logger.error(f"❌ فشل في إنشاء docker-compose.yml: {e}")
            return False
    
    def create_gitignore(self) -> bool:
        """إنشاء ملف .gitignore"""
        logger.info("📝 إنشاء ملف .gitignore...")
        
        gitignore_content = """# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Environment variables
.env
.env.local
.env.production

# IDE
.vscode/
.idea/
*.swp
*.swo

# Logs
logs/
*.log

# Database
*.db
*.sqlite3

# OS
.DS_Store
Thumbs.db

# Bot specific
user_*.json
admin_*.json
pending_*.json
security/
temp/
"""
        
        try:
            with open(".gitignore", 'w', encoding='utf-8') as f:
                f.write(gitignore_content)
            
            logger.info("✅ تم إنشاء ملف .gitignore")
            return True
            
        except Exception as e:
            logger.error(f"❌ فشل في إنشاء .gitignore: {e}")
            return False
    
    def prepare_for_deployment(self) -> Dict[str, bool]:
        """تجهيز المشروع للنشر"""
        logger.info("🚀 تجهيز المشروع للنشر...")
        
        tasks = {
            'prerequisites': self.check_prerequisites(),
            'procfile': self.create_procfile(),
            'runtime': self.create_runtime_txt(),
            'app_json': self.create_app_json(),
            'render_yaml': self.create_render_yaml(),
            'dockerfile': self.create_dockerfile(),
            'docker_compose': self.create_docker_compose(),
            'gitignore': self.create_gitignore()
        }
        
        return tasks
    
    def print_deployment_instructions(self):
        """طباعة تعليمات النشر"""
        print("\n" + "=" * 60)
        print("🚀 تعليمات النشر")
        print("=" * 60)
        
        print("\n📋 Heroku:")
        print("   1. heroku create your-bot-name")
        print("   2. heroku config:set BOT_TOKEN=your_token")
        print("   3. heroku config:set ADMIN_CHAT_ID=your_id")
        print("   4. heroku config:set SUPABASE_URL=your_url")
        print("   5. heroku config:set SUPABASE_KEY=your_key")
        print("   6. git push heroku main")
        
        print("\n📋 Render:")
        print("   1. ربط المستودع بـ Render")
        print("   2. اختيار 'Web Service'")
        print("   3. إضافة متغيرات البيئة")
        print("   4. النشر التلقائي")
        
        print("\n📋 Docker:")
        print("   1. docker build -t telegram-bot .")
        print("   2. docker run -d --env-file .env telegram-bot")
        
        print("\n📋 Railway:")
        print("   1. railway login")
        print("   2. railway new")
        print("   3. railway up")

def main():
    """الدالة الرئيسية"""
    print("🚀 أداة النشر التلقائي للبوت")
    print("=" * 50)
    
    deployer = BotDeployer()
    results = deployer.prepare_for_deployment()
    
    success_count = sum(results.values())
    total_count = len(results)
    
    print(f"\n📊 نتائج التجهيز:")
    print(f"   ✅ نجح: {success_count}/{total_count}")
    print(f"   ❌ فشل: {total_count - success_count}/{total_count}")
    
    if success_count == total_count:
        print("\n🎉 تم تجهيز المشروع للنشر بنجاح!")
        deployer.print_deployment_instructions()
        return 0
    else:
        print("\n⚠️ بعض المهام فشلت. يرجى مراجعة الأخطاء أعلاه.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
