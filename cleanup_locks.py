#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تنظيف الأقفال القديمة
Cleanup old locks
"""

import os
import sys
import logging
from pathlib import Path

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def cleanup_all_locks():
    """تنظيف جميع الأقفال القديمة"""
    logger.info("🧹 تنظيف جميع الأقفال القديمة...")
    
    lock_files = [
        "/tmp/telegram_bot_global.lock",
        "/tmp/bot_instance_info.json",
        "/tmp/main_py_lock.lock",
        "/tmp/bot_instance.lock",
        "/tmp/telegram_bot.lock",
        "/tmp/telegram_bot_heartbeat.json",
        "/tmp/bot_startup.lock"
    ]
    
    cleaned_count = 0
    
    for lock_file in lock_files:
        try:
            if os.path.exists(lock_file):
                os.remove(lock_file)
                logger.info(f"✅ تم حذف: {lock_file}")
                cleaned_count += 1
            else:
                logger.debug(f"⏭️ غير موجود: {lock_file}")
        except Exception as e:
            logger.error(f"❌ فشل في حذف {lock_file}: {e}")
    
    logger.info(f"🎉 تم تنظيف {cleaned_count} ملف قفل")
    
    if cleaned_count > 0:
        logger.info("✅ يمكنك الآن تشغيل البوت بأمان")
    else:
        logger.info("ℹ️ لا توجد أقفال للتنظيف")

def force_cleanup_telegram():
    """تنظيف قوي لتعارضات Telegram"""
    logger.info("🔧 تنظيف قوي لتعارضات Telegram...")
    
    try:
        import requests
        
        bot_token = os.getenv('BOT_TOKEN', '7605181405:AAEBua03X_QdD6GkCqKWR1P02EtXIbnSkx4')
        
        # مسح webhook
        try:
            response = requests.post(
                f"https://api.telegram.org/bot{bot_token}/deleteWebhook",
                json={"drop_pending_updates": True},
                timeout=30
            )
            if response.status_code == 200:
                logger.info("✅ تم مسح webhook")
            else:
                logger.warning(f"⚠️ فشل مسح webhook: {response.status_code}")
        except Exception as e:
            logger.error(f"❌ خطأ في مسح webhook: {e}")
        
        # مسح التحديثات المعلقة
        try:
            for attempt in range(5):
                response = requests.get(
                    f"https://api.telegram.org/bot{bot_token}/getUpdates",
                    params={"timeout": 0, "limit": 100},
                    timeout=30
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('ok') and result.get('result'):
                        updates = result['result']
                        if updates:
                            last_update_id = updates[-1]['update_id']
                            requests.get(
                                f"https://api.telegram.org/bot{bot_token}/getUpdates",
                                params={"offset": last_update_id + 1, "timeout": 0},
                                timeout=30
                            )
                            logger.info(f"✅ تم مسح {len(updates)} تحديث - المحاولة {attempt + 1}")
                        else:
                            logger.info("✅ لا توجد تحديثات معلقة")
                            break
                    else:
                        break
                else:
                    logger.warning(f"⚠️ فشل في جلب التحديثات: {response.status_code}")
                    break
        except Exception as e:
            logger.error(f"❌ خطأ في مسح التحديثات: {e}")
        
        logger.info("✅ تم التنظيف القوي لـ Telegram")
        
    except ImportError:
        logger.error("❌ مكتبة requests غير متوفرة")
    except Exception as e:
        logger.error(f"❌ خطأ في التنظيف القوي: {e}")

def main():
    """الدالة الرئيسية"""
    logger.info("🚀 بدء تنظيف الأقفال والتعارضات")
    logger.info("=" * 50)
    
    # تنظيف الأقفال
    cleanup_all_locks()
    
    # تنظيف Telegram
    force_cleanup_telegram()
    
    logger.info("=" * 50)
    logger.info("🎉 تم الانتهاء من التنظيف")
    logger.info("💡 يمكنك الآن تشغيل البوت بأمان")

if __name__ == "__main__":
    main()
