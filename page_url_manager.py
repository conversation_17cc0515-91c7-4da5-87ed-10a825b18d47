# ===================================================================
# مدير رابط الصفحة - Page URL Manager
# ===================================================================
# نظام إدارة رابط صفحة التحميل للمسؤول
# يسمح بتغيير الرابط عبر البوت بدلاً من التعديل اليدوي
# ===================================================================

import os
import json
import logging
import requests
from datetime import datetime, timezone
from typing import Optional, Dict, Any
from user_database_client import get_admin_setting, set_admin_setting

# إعداد التسجيل
logger = logging.getLogger(__name__)

# ===================================================================
# إعدادات مدير الصفحة
# ===================================================================

class PageURLConfig:
    # الرابط الافتراضي
    DEFAULT_PAGE_URL = "https://1c547fe5.sendaddons.pages.dev"
    
    # أنماط الروابط المقبولة
    ALLOWED_URL_PATTERNS = [
        r'^https://[a-zA-Z0-9\-]+\.pages\.dev/?.*$',  # Cloudflare Pages
        r'^https://[a-zA-Z0-9\-]+\.netlify\.app/?.*$',  # Netlify
        r'^https://[a-zA-Z0-9\-]+\.vercel\.app/?.*$',   # Vercel
        r'^https://[a-zA-Z0-9\-]+\.github\.io/?.*$',    # GitHub Pages
        r'^https://[a-zA-Z0-9\-\.]+\.com/?.*$',         # Custom domains
        r'^https://[a-zA-Z0-9\-\.]+\.net/?.*$',         # Custom domains
        r'^https://[a-zA-Z0-9\-\.]+\.org/?.*$',         # Custom domains
    ]
    
    # معلومات الصفحة المطلوبة
    REQUIRED_ENDPOINTS = [
        '/mod_details.html',
        '/style.css',
        '/script.js'
    ]

# ===================================================================
# مدير رابط الصفحة
# ===================================================================

class PageURLManager:
    def __init__(self):
        self.current_url = None
        self.url_history = []
        self.load_current_url()
    
    def load_current_url(self):
        """تحميل الرابط الحالي من قاعدة البيانات"""
        try:
            # محاولة جلب الرابط من قاعدة البيانات
            saved_url = get_admin_setting('page_url')
            
            if saved_url and isinstance(saved_url, str):
                self.current_url = saved_url
                logger.info(f"✅ تم تحميل رابط الصفحة من قاعدة البيانات: {self.current_url}")
            else:
                # استخدام الرابط الافتراضي
                self.current_url = PageURLConfig.DEFAULT_PAGE_URL
                self.save_current_url()
                logger.info(f"✅ تم تعيين الرابط الافتراضي: {self.current_url}")
            
            # تحديث متغير البيئة
            os.environ['WEB_SERVER_URL'] = self.current_url
            
        except Exception as e:
            logger.error(f"خطأ في تحميل رابط الصفحة: {e}")
            self.current_url = PageURLConfig.DEFAULT_PAGE_URL
            os.environ['WEB_SERVER_URL'] = self.current_url
    
    def save_current_url(self):
        """حفظ الرابط الحالي في قاعدة البيانات"""
        try:
            # حفظ في قاعدة البيانات
            success = set_admin_setting(
                'page_url', 
                self.current_url, 
                f"رابط صفحة التحميل - تم التحديث في {datetime.now().isoformat()}"
            )
            
            if success:
                logger.info(f"✅ تم حفظ رابط الصفحة في قاعدة البيانات: {self.current_url}")
            else:
                logger.error("❌ فشل في حفظ رابط الصفحة في قاعدة البيانات")
            
            # تحديث متغير البيئة
            os.environ['WEB_SERVER_URL'] = self.current_url
            
            # حفظ في ملف محلي كنسخة احتياطية
            self._save_to_local_file()
            
        except Exception as e:
            logger.error(f"خطأ في حفظ رابط الصفحة: {e}")
    
    def _save_to_local_file(self):
        """حفظ الرابط في ملف محلي كنسخة احتياطية"""
        try:
            os.makedirs('config', exist_ok=True)
            
            config_data = {
                'current_url': self.current_url,
                'last_updated': datetime.now().isoformat(),
                'url_history': self.url_history[-10:]  # آخر 10 روابط
            }
            
            with open('config/page_url_config.json', 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
                
            logger.info("✅ تم حفظ نسخة احتياطية من إعدادات الرابط")
            
        except Exception as e:
            logger.error(f"خطأ في حفظ النسخة الاحتياطية: {e}")
    
    def validate_url(self, url: str) -> tuple[bool, str]:
        """التحقق من صحة الرابط"""
        if not url or not isinstance(url, str):
            return False, "الرابط فارغ أو غير صالح"
        
        # تنظيف الرابط
        url = url.strip()
        if not url.startswith('http'):
            url = 'https://' + url
        
        # فحص النمط
        import re
        valid_pattern = False
        for pattern in PageURLConfig.ALLOWED_URL_PATTERNS:
            if re.match(pattern, url):
                valid_pattern = True
                break
        
        if not valid_pattern:
            return False, "نمط الرابط غير مدعوم. يجب أن يكون من مواقع الاستضافة المدعومة"
        
        # فحص إمكانية الوصول
        try:
            response = requests.head(url, timeout=10, allow_redirects=True)
            if response.status_code >= 400:
                return False, f"الرابط غير متاح (كود الخطأ: {response.status_code})"
        except requests.RequestException as e:
            return False, f"لا يمكن الوصول للرابط: {str(e)}"
        
        # فحص الملفات المطلوبة
        missing_files = []
        for endpoint in PageURLConfig.REQUIRED_ENDPOINTS:
            try:
                test_url = url.rstrip('/') + endpoint
                response = requests.head(test_url, timeout=5)
                if response.status_code >= 400:
                    missing_files.append(endpoint)
            except:
                missing_files.append(endpoint)
        
        if missing_files:
            return False, f"ملفات مفقودة في الصفحة: {', '.join(missing_files)}"
        
        return True, "الرابط صالح ويعمل بشكل صحيح"
    
    def update_url(self, new_url: str, admin_id: str) -> tuple[bool, str]:
        """تحديث رابط الصفحة"""
        try:
            # التحقق من صحة الرابط
            is_valid, message = self.validate_url(new_url)
            if not is_valid:
                return False, message
            
            # تنظيف الرابط
            new_url = new_url.strip()
            if not new_url.startswith('http'):
                new_url = 'https://' + new_url
            
            # حفظ الرابط القديم في التاريخ
            if self.current_url and self.current_url != new_url:
                self.url_history.append({
                    'url': self.current_url,
                    'replaced_at': datetime.now().isoformat(),
                    'replaced_by': admin_id
                })
            
            # تحديث الرابط
            old_url = self.current_url
            self.current_url = new_url
            
            # حفظ التغييرات
            self.save_current_url()
            
            # تسجيل التغيير
            logger.info(f"🔄 تم تحديث رابط الصفحة من {old_url} إلى {new_url} بواسطة {admin_id}")
            
            return True, f"✅ تم تحديث رابط الصفحة بنجاح!\n\nالرابط الجديد: {new_url}"
            
        except Exception as e:
            logger.error(f"خطأ في تحديث رابط الصفحة: {e}")
            return False, f"حدث خطأ أثناء تحديث الرابط: {str(e)}"
    
    def get_current_url(self) -> str:
        """الحصول على الرابط الحالي"""
        return self.current_url or PageURLConfig.DEFAULT_PAGE_URL
    
    def get_url_info(self) -> Dict[str, Any]:
        """الحصول على معلومات الرابط الحالي"""
        try:
            current_url = self.get_current_url()
            
            # فحص حالة الرابط
            try:
                response = requests.head(current_url, timeout=10)
                status = "✅ يعمل" if response.status_code < 400 else f"❌ خطأ ({response.status_code})"
                response_time = response.elapsed.total_seconds()
            except Exception as e:
                status = f"❌ غير متاح ({str(e)})"
                response_time = None
            
            # معلومات إضافية
            url_info = {
                'current_url': current_url,
                'status': status,
                'response_time': response_time,
                'last_updated': None,
                'total_changes': len(self.url_history)
            }
            
            # البحث عن آخر تحديث
            if self.url_history:
                url_info['last_updated'] = self.url_history[-1]['replaced_at']
            
            return url_info
            
        except Exception as e:
            logger.error(f"خطأ في جلب معلومات الرابط: {e}")
            return {
                'current_url': self.get_current_url(),
                'status': '❌ خطأ في جلب المعلومات',
                'response_time': None,
                'last_updated': None,
                'total_changes': 0
            }
    
    def get_url_history(self, limit: int = 10) -> list:
        """الحصول على تاريخ تغييرات الرابط"""
        return self.url_history[-limit:] if self.url_history else []
    
    def test_url_compatibility(self, url: str) -> Dict[str, Any]:
        """اختبار توافق الرابط مع البوت"""
        try:
            results = {
                'url': url,
                'overall_status': True,
                'tests': {}
            }
            
            # اختبار الوصول الأساسي
            try:
                response = requests.get(url, timeout=10)
                results['tests']['basic_access'] = {
                    'status': response.status_code < 400,
                    'code': response.status_code,
                    'message': 'يمكن الوصول للصفحة' if response.status_code < 400 else f'خطأ {response.status_code}'
                }
            except Exception as e:
                results['tests']['basic_access'] = {
                    'status': False,
                    'code': None,
                    'message': f'لا يمكن الوصول: {str(e)}'
                }
                results['overall_status'] = False
            
            # اختبار الملفات المطلوبة
            for endpoint in PageURLConfig.REQUIRED_ENDPOINTS:
                test_url = url.rstrip('/') + endpoint
                try:
                    response = requests.head(test_url, timeout=5)
                    results['tests'][f'file_{endpoint}'] = {
                        'status': response.status_code < 400,
                        'code': response.status_code,
                        'message': 'موجود' if response.status_code < 400 else 'مفقود'
                    }
                    if response.status_code >= 400:
                        results['overall_status'] = False
                except Exception as e:
                    results['tests'][f'file_{endpoint}'] = {
                        'status': False,
                        'code': None,
                        'message': f'خطأ: {str(e)}'
                    }
                    results['overall_status'] = False
            
            # اختبار سرعة الاستجابة
            try:
                import time
                start_time = time.time()
                response = requests.head(url, timeout=10)
                response_time = time.time() - start_time
                
                results['tests']['response_time'] = {
                    'status': response_time < 5.0,
                    'time': response_time,
                    'message': f'{response_time:.2f} ثانية' + (' (سريع)' if response_time < 2.0 else ' (بطيء)' if response_time > 5.0 else ' (مقبول)')
                }
                
                if response_time > 10.0:
                    results['overall_status'] = False
                    
            except Exception as e:
                results['tests']['response_time'] = {
                    'status': False,
                    'time': None,
                    'message': f'خطأ في قياس السرعة: {str(e)}'
                }
            
            return results
            
        except Exception as e:
            logger.error(f"خطأ في اختبار توافق الرابط: {e}")
            return {
                'url': url,
                'overall_status': False,
                'tests': {
                    'error': {
                        'status': False,
                        'message': f'خطأ عام: {str(e)}'
                    }
                }
            }
    
    def rollback_to_previous_url(self, admin_id: str) -> tuple[bool, str]:
        """العودة للرابط السابق"""
        try:
            if not self.url_history:
                return False, "لا يوجد رابط سابق للعودة إليه"
            
            # الحصول على آخر رابط
            previous_entry = self.url_history[-1]
            previous_url = previous_entry['url']
            
            # التحقق من صحة الرابط السابق
            is_valid, message = self.validate_url(previous_url)
            if not is_valid:
                return False, f"الرابط السابق لم يعد صالحاً: {message}"
            
            # حفظ الرابط الحالي في التاريخ
            self.url_history.append({
                'url': self.current_url,
                'replaced_at': datetime.now().isoformat(),
                'replaced_by': admin_id,
                'action': 'rollback'
            })
            
            # تحديث الرابط
            old_url = self.current_url
            self.current_url = previous_url
            
            # حفظ التغييرات
            self.save_current_url()
            
            # تسجيل التغيير
            logger.info(f"↩️ تم الرجوع للرابط السابق من {old_url} إلى {previous_url} بواسطة {admin_id}")
            
            return True, f"✅ تم الرجوع للرابط السابق بنجاح!\n\nالرابط: {previous_url}"
            
        except Exception as e:
            logger.error(f"خطأ في الرجوع للرابط السابق: {e}")
            return False, f"حدث خطأ أثناء الرجوع للرابط السابق: {str(e)}"

# ===================================================================
# مدير الصفحة العالمي
# ===================================================================

# إنشاء مثيل عالمي
page_url_manager = PageURLManager()

# ===================================================================
# دوال مساعدة
# ===================================================================

def get_current_page_url() -> str:
    """الحصول على رابط الصفحة الحالي"""
    return page_url_manager.get_current_url()

def update_page_url(new_url: str, admin_id: str) -> tuple[bool, str]:
    """تحديث رابط الصفحة"""
    return page_url_manager.update_url(new_url, admin_id)

def get_page_url_info() -> Dict[str, Any]:
    """الحصول على معلومات رابط الصفحة"""
    return page_url_manager.get_url_info()

def test_page_url(url: str) -> Dict[str, Any]:
    """اختبار رابط الصفحة"""
    return page_url_manager.test_url_compatibility(url)
