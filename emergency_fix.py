#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح طارئ لمشاكل المسافات البادئة
"""

def emergency_fix():
    """إصلاح طارئ للمشاكل الأساسية"""
    
    with open('main.py', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    fixed_lines = []
    in_main_function = False
    try_level = 0
    
    for i, line in enumerate(lines):
        line_num = i + 1
        stripped = line.strip()
        
        # تحديد بداية دالة main
        if 'async def main():' in line:
            in_main_function = True
            fixed_lines.append(line)
            continue
        
        # تحديد نهاية دالة main
        if in_main_function and (line.startswith('def ') or line.startswith('async def ') or line.startswith('class ') or line.startswith('if __name__')):
            in_main_function = False
            fixed_lines.append(line)
            continue
        
        if not in_main_function:
            fixed_lines.append(line)
            continue
        
        # معالجة محتوى دالة main
        if not stripped or stripped.startswith('#'):
            fixed_lines.append(line)
            continue
        
        # إصلاح المسافات البادئة
        if stripped.startswith('try:'):
            try_level += 1
            fixed_lines.append('    ' + stripped + '\n')
        elif stripped.startswith(('except', 'finally')):
            indent = '    ' * try_level
            fixed_lines.append(indent + stripped + '\n')
        else:
            # محتوى عادي
            indent = '    ' + '    ' * try_level
            fixed_lines.append(indent + stripped + '\n')
    
    # كتابة الملف المصحح
    with open('main.py', 'w', encoding='utf-8') as f:
        f.writelines(fixed_lines)
    
    print("✅ تم الإصلاح الطارئ")

def check_syntax():
    """فحص سريع للكود"""
    try:
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        compile(content, 'main.py', 'exec')
        print("✅ الكود صحيح")
        return True
    except SyntaxError as e:
        print(f"❌ خطأ في السطر {e.lineno}: {e.msg}")
        return False

if __name__ == "__main__":
    print("🚨 بدء الإصلاح الطارئ...")
    emergency_fix()
    check_syntax()
