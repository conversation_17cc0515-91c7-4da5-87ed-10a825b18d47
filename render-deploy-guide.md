# 🚀 دليل رفع البوت على Render

## الخطوة 1: إنشاء حساب
1. اذهب إلى [render.com](https://render.com)
2. اضغط "Get Started for Free"
3. سجل دخول بـ GitHub أو Google أو Email

## الخطوة 2: إن<PERSON><PERSON><PERSON> خدمة ويب
1. من لوحة التحكم اضغط "New +"
2. اختر "Web Service"
3. اختر "Build and deploy from a Git repository"
4. اضغط "Connect a repository" ثم "Upload files"

## الخطوة 3: رفع الملفات
1. اضغط "Browse" واختر جميع ملفات البوت
2. أو اسحب الملفات إلى المنطقة المخصصة
3. تأكد من رفع:
   - main.py
   - requirements.txt
   - runtime.txt (اختياري)
   - جميع ملفات JSON
   - render.yaml (موجود في مشروعك)

## الخطوة 4: إعداد الخدمة
1. **Name**: اختر اسم للبوت (مثل: minecraft-mods-bot)
2. **Region**: اختر أقرب منطقة لك
3. **Branch**: main (افتراضي)
4. **Runtime**: Python 3
5. **Build Command**: `pip install -r requirements.txt`
6. **Start Command**: `python main.py`

## الخطوة 5: إعداد متغيرات البيئة
في قسم "Environment Variables" أضف:
```
BOT_TOKEN=your_bot_token_here
ADMIN_CHAT_ID=your_admin_id_here
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_key
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=INFO
PYTHON_VERSION=3.11.0
```

## الخطوة 6: النشر
1. اضغط "Create Web Service"
2. انتظر حتى ينتهي البناء والنشر
3. راقب اللوجز للتأكد من عدم وجود أخطاء

## الخطوة 7: منع النوم (اختياري)
لمنع الخدمة من النوم، أضف هذا الكود في main.py:
```python
# إضافة ping endpoint لمنع النوم
from flask import Flask
app = Flask(__name__)

@app.route('/ping')
def ping():
    return 'Bot is alive!'

# تشغيل Flask في thread منفصل
import threading
def run_flask():
    app.run(host='0.0.0.0', port=int(os.environ.get('PORT', 5000)))

flask_thread = threading.Thread(target=run_flask)
flask_thread.daemon = True
flask_thread.start()
```

## نصائح مهمة:
- ✅ مجاني تماماً
- ✅ SSL تلقائي
- ⚠️ ينام بعد 15 دقيقة من عدم النشاط
- ⚠️ يستيقظ عند أول طلب (قد يستغرق 30 ثانية)
- 💡 استخدم خدمة ping خارجية لمنع النوم
