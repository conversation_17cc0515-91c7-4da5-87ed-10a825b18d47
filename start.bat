@echo off
chcp 65001 >nul
title Bot Telegram - تشغيل البوت

echo 🚀 بدء تشغيل البوت...
echo 🚀 Starting Bot...
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت
    echo ❌ Python is not installed
    pause
    exit /b 1
)

REM التحقق من وجود pip
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ pip غير مثبت
    echo ❌ pip is not installed
    pause
    exit /b 1
)

REM التحقق من وجود ملف .env
if not exist ".env" (
    echo ⚠️ ملف .env غير موجود
    echo ⚠️ .env file not found
    echo.
    
    if exist ".env.example" (
        echo 📝 نسخ .env.example إلى .env
        echo 📝 Copying .env.example to .env
        copy ".env.example" ".env" >nul
        echo ✅ تم إنشاء ملف .env
        echo ✅ .env file created
        echo.
        echo 📋 يرجى تحديث ملف .env بالمعلومات الصحيحة
        echo 📋 Please update .env file with correct information
        pause
        exit /b 1
    ) else (
        echo ❌ ملف .env.example غير موجود
        echo ❌ .env.example file not found
        pause
        exit /b 1
    )
)

REM تثبيت المتطلبات
echo 📦 تثبيت المتطلبات...
echo 📦 Installing requirements...
echo.

if exist "requirements.txt" (
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ فشل في تثبيت المتطلبات
        echo ❌ Failed to install requirements
        pause
        exit /b 1
    ) else (
        echo ✅ تم تثبيت المتطلبات بنجاح
        echo ✅ Requirements installed successfully
        echo.
    )
) else (
    echo ❌ ملف requirements.txt غير موجود
    echo ❌ requirements.txt file not found
    pause
    exit /b 1
)

REM إنشاء المجلدات المطلوبة
echo 📁 إنشاء المجلدات المطلوبة...
echo 📁 Creating required directories...

if not exist "logs" mkdir logs
if not exist "temp" mkdir temp
if not exist "user_customizations" mkdir user_customizations

echo ✅ تم إنشاء المجلدات
echo ✅ Directories created
echo.

REM تشغيل البوت
echo 🤖 تشغيل البوت...
echo 🤖 Starting bot...
echo.
echo ========================================
echo.

python main.py

echo.
echo ========================================
echo 🛑 تم إيقاف البوت
echo 🛑 Bot stopped
echo.
pause
