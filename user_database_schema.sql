-- ===================================================================
-- ملف SQL لإنشاء قاعدة بيانات المستخدمين في Supabase
-- User Database Schema for Supabase
-- ===================================================================
-- هذا الملف يحتوي على جميع الجداول المطلوبة لتخزين بيانات وإعدادات المستخدمين
-- بدلاً من التخزين المحلي في ملفات JSON
-- ===================================================================

-- ===================================================================
-- 1. جدول المستخدمين الأساسي (Users Table)
-- يحل محل all_users.json
-- ===================================================================

CREATE TABLE IF NOT EXISTS public.users (
    id SERIAL PRIMARY KEY,
    user_id TEXT NOT NULL UNIQUE,           -- معرف المستخدم في تليجرام
    username TEXT,                          -- اسم المستخدم
    full_name TEXT,                         -- الاسم الكامل
    lang TEXT DEFAULT 'ar' CHECK (lang IN ('ar', 'en')), -- اللغة المفضلة
    first_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),   -- أول ظهور
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(), -- آخر نشاط
    is_active BOOLEAN DEFAULT true,         -- حالة النشاط
    is_banned BOOLEAN DEFAULT false,        -- حالة الحظر
    ban_reason TEXT,                        -- سبب الحظر
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- فهرس لتسريع البحث
CREATE INDEX IF NOT EXISTS idx_users_user_id ON public.users(user_id);
CREATE INDEX IF NOT EXISTS idx_users_last_activity ON public.users(last_activity);

-- ===================================================================
-- 2. جدول القنوات (Channels Table)
-- يحل محل user_channels.json
-- ===================================================================

CREATE TABLE IF NOT EXISTS public.user_channels (
    id SERIAL PRIMARY KEY,
    user_id TEXT NOT NULL,                  -- معرف المستخدم
    channel_id TEXT NOT NULL,               -- معرف القناة
    channel_name TEXT,                      -- اسم القناة
    is_default BOOLEAN DEFAULT false,       -- القناة الافتراضية
    is_active BOOLEAN DEFAULT true,         -- حالة النشاط
    publish_interval INTEGER DEFAULT 60,    -- فترة النشر بالدقائق
    preview_enabled BOOLEAN DEFAULT false,  -- تفعيل المعاينة
    last_publish_time TIMESTAMP WITH TIME ZONE, -- آخر وقت نشر
    message_format TEXT DEFAULT 'classic' CHECK (message_format IN ('classic', 'modern', 'minimal')),
    channel_lang TEXT DEFAULT 'ar' CHECK (channel_lang IN ('ar', 'en')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- قيود فريدة
    UNIQUE(user_id, channel_id),
    
    -- مرجع للمستخدم
    FOREIGN KEY (user_id) REFERENCES public.users(user_id) ON DELETE CASCADE
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_user_channels_user_id ON public.user_channels(user_id);
CREATE INDEX IF NOT EXISTS idx_user_channels_channel_id ON public.user_channels(channel_id);
CREATE INDEX IF NOT EXISTS idx_user_channels_active ON public.user_channels(is_active);

-- ===================================================================
-- 3. جدول فئات المودات المسموحة للقنوات (Channel Categories)
-- ===================================================================

CREATE TABLE IF NOT EXISTS public.channel_mod_categories (
    id SERIAL PRIMARY KEY,
    user_id TEXT NOT NULL,
    channel_id TEXT NOT NULL,
    category TEXT NOT NULL CHECK (category IN ('addons', 'shaders', 'texture_packs', 'seeds', 'maps')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- قيود فريدة
    UNIQUE(user_id, channel_id, category),
    
    -- مرجع للقناة
    FOREIGN KEY (user_id, channel_id) REFERENCES public.user_channels(user_id, channel_id) ON DELETE CASCADE
);

-- ===================================================================
-- 4. جدول إصدارات ماين كرافت المسموحة للقنوات (Channel MC Versions)
-- ===================================================================

CREATE TABLE IF NOT EXISTS public.channel_mc_versions (
    id SERIAL PRIMARY KEY,
    user_id TEXT NOT NULL,
    channel_id TEXT NOT NULL,
    mc_version TEXT NOT NULL,               -- إصدار ماين كرافت مثل "1.21+", "1.20+"
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- قيود فريدة
    UNIQUE(user_id, channel_id, mc_version),
    
    -- مرجع للقناة
    FOREIGN KEY (user_id, channel_id) REFERENCES public.user_channels(user_id, channel_id) ON DELETE CASCADE
);

-- ===================================================================
-- 5. جدول اشتراكات المستخدمين (User Subscriptions)
-- يحل محل user_subscriptions.json
-- ===================================================================

CREATE TABLE IF NOT EXISTS public.user_subscriptions (
    id SERIAL PRIMARY KEY,
    user_id TEXT NOT NULL,
    subscription_type TEXT NOT NULL CHECK (subscription_type IN ('free', 'premium', 'vip')),
    start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    end_date TIMESTAMP WITH TIME ZONE,      -- null للاشتراك المجاني
    is_active BOOLEAN DEFAULT true,
    auto_renew BOOLEAN DEFAULT false,
    payment_method TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- مرجع للمستخدم
    FOREIGN KEY (user_id) REFERENCES public.users(user_id) ON DELETE CASCADE
);

-- فهرس للبحث السريع
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_user_id ON public.user_subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_active ON public.user_subscriptions(is_active);

-- ===================================================================
-- 6. جدول تفعيل المميزات (Feature Activation)
-- يحل محل user_feature_activation.json
-- ===================================================================

CREATE TABLE IF NOT EXISTS public.user_feature_activation (
    id SERIAL PRIMARY KEY,
    user_id TEXT NOT NULL,
    feature_name TEXT NOT NULL,             -- اسم الميزة
    is_enabled BOOLEAN DEFAULT false,       -- حالة التفعيل
    activation_date TIMESTAMP WITH TIME ZONE,
    expiry_date TIMESTAMP WITH TIME ZONE,   -- تاريخ انتهاء الصلاحية
    activation_source TEXT,                 -- مصدر التفعيل (subscription, admin, trial)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- قيود فريدة
    UNIQUE(user_id, feature_name),
    
    -- مرجع للمستخدم
    FOREIGN KEY (user_id) REFERENCES public.users(user_id) ON DELETE CASCADE
);

-- فهرس للبحث السريع
CREATE INDEX IF NOT EXISTS idx_user_feature_activation_user_id ON public.user_feature_activation(user_id);
CREATE INDEX IF NOT EXISTS idx_user_feature_activation_enabled ON public.user_feature_activation(is_enabled);

-- ===================================================================
-- 7. جدول تقييمات المستخدمين (User Feedback)
-- يحل محل user_feedback.json
-- ===================================================================

CREATE TABLE IF NOT EXISTS public.user_feedback (
    id SERIAL PRIMARY KEY,
    user_id TEXT NOT NULL,
    mod_id INTEGER NOT NULL,
    feedback_type TEXT CHECK (feedback_type IN ('like', 'dislike', 'report')),
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    comment TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- قيود فريدة - مستخدم واحد يمكنه تقييم مود واحد مرة واحدة فقط
    UNIQUE(user_id, mod_id),
    
    -- مرجع للمستخدم
    FOREIGN KEY (user_id) REFERENCES public.users(user_id) ON DELETE CASCADE
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_user_feedback_user_id ON public.user_feedback(user_id);
CREATE INDEX IF NOT EXISTS idx_user_feedback_mod_id ON public.user_feedback(mod_id);
CREATE INDEX IF NOT EXISTS idx_user_feedback_type ON public.user_feedback(feedback_type);

-- ===================================================================
-- 8. جدول حالة المودات للمستخدمين (User Mods Status)
-- يحل محل user_mods_status.json
-- ===================================================================

CREATE TABLE IF NOT EXISTS public.user_mods_status (
    id SERIAL PRIMARY KEY,
    user_id TEXT NOT NULL,
    mod_id INTEGER NOT NULL,
    status TEXT DEFAULT 'published' CHECK (status IN ('published', 'skipped', 'pending')),
    published_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    channel_id TEXT,                        -- القناة التي تم النشر فيها
    
    -- قيود فريدة
    UNIQUE(user_id, mod_id),
    
    -- مرجع للمستخدم
    FOREIGN KEY (user_id) REFERENCES public.users(user_id) ON DELETE CASCADE
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_user_mods_status_user_id ON public.user_mods_status(user_id);
CREATE INDEX IF NOT EXISTS idx_user_mods_status_mod_id ON public.user_mods_status(mod_id);
CREATE INDEX IF NOT EXISTS idx_user_mods_status_status ON public.user_mods_status(status);

-- ===================================================================
-- 9. جدول المودات المحظورة للمستخدمين (User Blocked Mods)
-- يحل محل user_blocked_mods.json
-- ===================================================================

CREATE TABLE IF NOT EXISTS public.user_blocked_mods (
    id SERIAL PRIMARY KEY,
    user_id TEXT NOT NULL,
    mod_id INTEGER NOT NULL,
    blocked_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    reason TEXT,                            -- سبب الحظر
    
    -- قيود فريدة
    UNIQUE(user_id, mod_id),
    
    -- مرجع للمستخدم
    FOREIGN KEY (user_id) REFERENCES public.users(user_id) ON DELETE CASCADE
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_user_blocked_mods_user_id ON public.user_blocked_mods(user_id);
CREATE INDEX IF NOT EXISTS idx_user_blocked_mods_mod_id ON public.user_blocked_mods(mod_id);

-- ===================================================================
-- 10. جدول نظام الدعوات (User Invitations)
-- يحل محل user_invitations.json
-- ===================================================================

CREATE TABLE IF NOT EXISTS public.user_invitations (
    id SERIAL PRIMARY KEY,
    inviter_user_id TEXT NOT NULL,          -- المستخدم الذي أرسل الدعوة
    invited_user_id TEXT,                   -- المستخدم المدعو (null إذا لم ينضم بعد)
    invitation_code TEXT NOT NULL UNIQUE,   -- كود الدعوة
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'expired')),
    reward_claimed BOOLEAN DEFAULT false,   -- هل تم استلام المكافأة
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    accepted_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    
    -- مرجع للمستخدم المدعو
    FOREIGN KEY (inviter_user_id) REFERENCES public.users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (invited_user_id) REFERENCES public.users(user_id) ON DELETE SET NULL
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_user_invitations_inviter ON public.user_invitations(inviter_user_id);
CREATE INDEX IF NOT EXISTS idx_user_invitations_code ON public.user_invitations(invitation_code);
CREATE INDEX IF NOT EXISTS idx_user_invitations_status ON public.user_invitations(status);

-- ===================================================================
-- 11. جدول إعدادات المسؤول (Admin Settings)
-- يحل محل admin_settings.json
-- ===================================================================

CREATE TABLE IF NOT EXISTS public.admin_settings (
    id SERIAL PRIMARY KEY,
    setting_key TEXT NOT NULL UNIQUE,       -- مفتاح الإعداد
    setting_value JSONB NOT NULL,           -- قيمة الإعداد (JSON)
    description TEXT,                       -- وصف الإعداد
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- فهرس للبحث السريع
CREATE INDEX IF NOT EXISTS idx_admin_settings_key ON public.admin_settings(setting_key);

-- ===================================================================
-- 12. جدول قائمة انتظار النشر (Pending Publications)
-- يحل محل pending_publication.json
-- ===================================================================

CREATE TABLE IF NOT EXISTS public.pending_publications (
    id SERIAL PRIMARY KEY,
    user_id TEXT NOT NULL,
    mod_id INTEGER NOT NULL,
    channel_id TEXT,
    status TEXT DEFAULT 'awaiting_admin_approval' CHECK (status IN ('awaiting_user_approval', 'awaiting_admin_approval', 'approved', 'rejected')),
    proposal_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status_update_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    admin_notes TEXT,                       -- ملاحظات المسؤول
    
    -- قيود فريدة
    UNIQUE(user_id, mod_id),
    
    -- مرجع للمستخدم
    FOREIGN KEY (user_id) REFERENCES public.users(user_id) ON DELETE CASCADE
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_pending_publications_user_id ON public.pending_publications(user_id);
CREATE INDEX IF NOT EXISTS idx_pending_publications_mod_id ON public.pending_publications(mod_id);
CREATE INDEX IF NOT EXISTS idx_pending_publications_status ON public.pending_publications(status);
CREATE INDEX IF NOT EXISTS idx_pending_publications_proposal_time ON public.pending_publications(proposal_time);

-- ===================================================================
-- 13. إدراج الإعدادات الافتراضية للمسؤول
-- ===================================================================

INSERT INTO public.admin_settings (setting_key, setting_value, description) VALUES
('admin_preview_required', 'true', 'هل يتطلب موافقة المسؤول قبل النشر'),
('global_features_enabled', '{
    "unlimited_channels": true,
    "url_shortener_access": true,
    "custom_download_links": true,
    "publish_intervals_extended": true,
    "tasks_system_access": true,
    "page_customization_vip": true,
    "ads_system_access": true
}', 'إعدادات المميزات العامة للنظام')
ON CONFLICT (setting_key) DO NOTHING;

-- ===================================================================
-- 14. دوال مساعدة (Helper Functions)
-- ===================================================================

-- دالة للحصول على القناة الافتراضية للمستخدم
CREATE OR REPLACE FUNCTION get_user_default_channel(p_user_id TEXT)
RETURNS TEXT AS $$
BEGIN
    RETURN (
        SELECT channel_id 
        FROM public.user_channels 
        WHERE user_id = p_user_id AND is_default = true AND is_active = true
        LIMIT 1
    );
END;
$$ LANGUAGE plpgsql;

-- دالة للتحقق من تفعيل ميزة للمستخدم
CREATE OR REPLACE FUNCTION is_user_feature_enabled(p_user_id TEXT, p_feature_name TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN COALESCE((
        SELECT is_enabled 
        FROM public.user_feature_activation 
        WHERE user_id = p_user_id AND feature_name = p_feature_name
        AND (expiry_date IS NULL OR expiry_date > NOW())
    ), false);
END;
$$ LANGUAGE plpgsql;

-- دالة للحصول على إعداد المسؤول
CREATE OR REPLACE FUNCTION get_admin_setting(p_setting_key TEXT)
RETURNS JSONB AS $$
BEGIN
    RETURN (
        SELECT setting_value 
        FROM public.admin_settings 
        WHERE setting_key = p_setting_key
    );
END;
$$ LANGUAGE plpgsql;

-- ===================================================================
-- 15. تحديث الطوابع الزمنية تلقائياً
-- ===================================================================

-- دالة لتحديث updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إضافة المحفزات للجداول التي تحتاج تحديث updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_channels_updated_at BEFORE UPDATE ON public.user_channels
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_subscriptions_updated_at BEFORE UPDATE ON public.user_subscriptions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_feature_activation_updated_at BEFORE UPDATE ON public.user_feature_activation
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_admin_settings_updated_at BEFORE UPDATE ON public.admin_settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ===================================================================
-- انتهى الملف
-- End of file
-- ===================================================================
