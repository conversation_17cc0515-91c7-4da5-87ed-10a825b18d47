#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل البوت مع ضمان instance واحد فقط
Single instance bot starter - prevents multiple instances
"""

import os
import sys
import time
import logging
import asyncio
import signal
import threading
import requests
from pathlib import Path
from datetime import datetime
import json

# استيراد مدير القفل المحسن
try:
    from instance_lock_manager import ensure_single_instance, release_instance_lock
    LOCK_MANAGER_AVAILABLE = True
except ImportError:
    LOCK_MANAGER_AVAILABLE = False

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

# ملف القفل لمنع التشغيل المتعدد
LOCK_FILE = "/tmp/bot_instance.lock"
HEALTH_CHECK_PORT = int(os.getenv('PORT', 10000))

class SingleInstanceManager:
    """مدير ضمان instance واحد فقط"""
    
    def __init__(self):
        self.lock_acquired = False
        self.health_server = None
        
    def acquire_lock(self):
        """محاولة الحصول على القفل"""
        try:
            if os.path.exists(LOCK_FILE):
                # فحص إذا كان البوت الآخر لا يزال يعمل
                with open(LOCK_FILE, 'r') as f:
                    data = json.load(f)
                    old_pid = data.get('pid')
                    start_time = data.get('start_time')
                
                # فحص إذا كان PID لا يزال نشطاً
                if old_pid and self._is_process_running(old_pid):
                    logger.warning(f"⚠️ البوت يعمل بالفعل (PID: {old_pid})")
                    return False
                else:
                    logger.info("🔄 إزالة ملف قفل قديم...")
                    os.remove(LOCK_FILE)
            
            # إنشاء ملف قفل جديد
            lock_data = {
                'pid': os.getpid(),
                'start_time': datetime.now().isoformat(),
                'host': os.getenv('RENDER_INSTANCE_ID', 'unknown')
            }
            
            with open(LOCK_FILE, 'w') as f:
                json.dump(lock_data, f)
            
            self.lock_acquired = True
            logger.info(f"✅ تم الحصول على القفل (PID: {os.getpid()})")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على القفل: {e}")
            return False
    
    def _is_process_running(self, pid):
        """فحص إذا كان العملية لا تزال تعمل"""
        try:
            os.kill(pid, 0)
            return True
        except OSError:
            return False
    
    def release_lock(self):
        """تحرير القفل"""
        if self.lock_acquired and os.path.exists(LOCK_FILE):
            try:
                os.remove(LOCK_FILE)
                logger.info("🔓 تم تحرير القفل")
            except Exception as e:
                logger.error(f"❌ خطأ في تحرير القفل: {e}")

def setup_environment():
    """إعداد البيئة الأساسية"""
    logger.info("🔧 إعداد البيئة...")
    
    # متغيرات البيئة الأساسية
    env_vars = {
        'BOT_TOKEN': '**********************************************',
        'TELEGRAM_BOT_TOKEN': '**********************************************',
        'SUPABASE_URL': 'https://ytqxxodyecdeosnqoure.supabase.co',
        'SUPABASE_KEY': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4',
        'ADMIN_CHAT_ID': '7513880877',
        'USE_NGROK': 'false',
        'NGROK_ENABLED': 'false',
        'ENVIRONMENT': 'production',
        'DEBUG': 'false',
        'PYTHONUNBUFFERED': '1',
        'PYTHONIOENCODING': 'utf-8',
        'RENDER': 'true',
        'WEB_SERVER_URL': 'https://1c547fe5.sendaddons.pages.dev'
    }
    
    for key, value in env_vars.items():
        if not os.getenv(key):
            os.environ[key] = str(value)
    
    # إنشاء المجلدات المطلوبة
    required_dirs = ['logs', 'temp', 'cache', 'security/logs', 'security/quarantine', 'security_logs']
    for dir_path in required_dirs:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
    
    # إنشاء الملفات المطلوبة
    required_files = [
        'user_channels.json', 'all_users.json', 'user_feedback.json',
        'user_mods_status.json', 'user_blocked_mods.json', 'user_invitations.json',
        'user_subscriptions.json', 'user_feature_activation.json',
        'admin_settings.json', 'admin_processed_mods.json', 'pending_publication.json'
    ]
    
    for file_path in required_files:
        if not Path(file_path).exists():
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write('{}')
    
    logger.info("✅ تم إعداد البيئة")

def clear_telegram_conflicts():
    """مسح تعارضات Telegram"""
    logger.info("🔧 مسح تعارضات Telegram...")
    
    try:
        bot_token = os.getenv('BOT_TOKEN')
        if not bot_token:
            logger.warning("⚠️ لم يتم العثور على BOT_TOKEN")
            return False
        
        # مسح webhook
        try:
            response = requests.post(
                f"https://api.telegram.org/bot{bot_token}/deleteWebhook",
                json={"drop_pending_updates": True},
                timeout=15
            )
            if response.status_code == 200 and response.json().get('ok'):
                logger.info("✅ تم مسح webhook")
        except Exception as e:
            logger.warning(f"⚠️ تحذير في مسح webhook: {e}")
        
        # مسح التحديثات المعلقة بطريقة شاملة
        try:
            for attempt in range(3):
                response = requests.get(
                    f"https://api.telegram.org/bot{bot_token}/getUpdates",
                    params={"timeout": 1, "limit": 100},
                    timeout=15
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('ok') and result.get('result'):
                        updates = result['result']
                        if updates:
                            last_update_id = updates[-1]['update_id']
                            requests.get(
                                f"https://api.telegram.org/bot{bot_token}/getUpdates",
                                params={"offset": last_update_id + 1, "timeout": 1},
                                timeout=10
                            )
                            logger.info(f"✅ تم مسح {len(updates)} تحديث معلق")
                        else:
                            break
                    else:
                        break
                else:
                    break
        except Exception as e:
            logger.warning(f"⚠️ تحذير في مسح التحديثات: {e}")
        
        # انتظار قصير للتأكد
        time.sleep(3)
        logger.info("✅ تم مسح تعارضات Telegram")
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في مسح تعارضات Telegram: {e}")
        return False

def start_health_server():
    """تشغيل خادم الصحة"""
    try:
        from flask import Flask, jsonify
        
        app = Flask(__name__)
        
        @app.route('/')
        def health():
            return jsonify({
                "status": "healthy",
                "service": "minecraft_mods_bot",
                "timestamp": str(datetime.now()),
                "platform": "render",
                "pid": os.getpid()
            })
        
        @app.route('/health')
        def health_check():
            return jsonify({"status": "ok", "pid": os.getpid()})
        
        def run_server():
            app.run(host='0.0.0.0', port=HEALTH_CHECK_PORT, debug=False, use_reloader=False)
        
        server_thread = threading.Thread(target=run_server, daemon=True)
        server_thread.start()
        
        logger.info(f"🌐 خادم الصحة يعمل على المنفذ {HEALTH_CHECK_PORT}")
        return True
        
    except Exception as e:
        logger.error(f"❌ فشل في تشغيل خادم الصحة: {e}")
        return False

async def start_bot():
    """تشغيل البوت الرئيسي"""
    logger.info("🤖 بدء تشغيل البوت...")
    
    try:
        # استيراد البوت الرئيسي
        import main
        
        # تشغيل البوت
        if hasattr(main, 'main') and asyncio.iscoroutinefunction(main.main):
            await main.main()
        elif hasattr(main, 'main'):
            main.main()
        else:
            logger.error("❌ لم يتم العثور على دالة main في البوت")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل البوت: {e}")
        return False

def setup_signal_handlers(instance_manager):
    """إعداد معالجات الإشارات"""
    def signal_handler(signum, frame):
        logger.info(f"📡 تم استلام إشارة {signum}، إغلاق آمن...")
        instance_manager.release_lock()
        sys.exit(0)
    
    signal.signal(signal.SIGTERM, signal_handler)
    signal.signal(signal.SIGINT, signal_handler)

async def main():
    """الدالة الرئيسية"""
    logger.info("🚀 بدء تشغيل البوت - وضع Instance واحد")
    logger.info("=" * 50)

    try:
        # استخدام مدير القفل المحسن إذا كان متوفراً
        if LOCK_MANAGER_AVAILABLE:
            ensure_single_instance()
        else:
            # استخدام المدير التقليدي
            instance_manager = SingleInstanceManager()
            setup_signal_handlers(instance_manager)

            if not instance_manager.acquire_lock():
                logger.warning("⚠️ البوت يعمل بالفعل، إنهاء هذا Instance")
                sys.exit(0)
        
        # إعداد البيئة
        setup_environment()
        
        # مسح تعارضات Telegram
        clear_telegram_conflicts()
        
        # تشغيل خادم الصحة
        start_health_server()
        
        # تشغيل البوت
        await start_bot()
        
    except KeyboardInterrupt:
        logger.info("⏹️ تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        logger.error(f"❌ خطأ في التشغيل: {e}")
        sys.exit(1)
    finally:
        # تحرير القفل
        if LOCK_MANAGER_AVAILABLE:
            release_instance_lock()
        else:
            try:
                instance_manager.release_lock()
            except:
                pass

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("⏹️ تم إيقاف البوت")
    except Exception as e:
        logger.error(f"❌ خطأ في التشغيل: {e}")
        sys.exit(1)
