#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام بدء التشغيل الموحد - يتجنب التعارضات ويضمن التشغيل السليم
Unified startup system - avoids conflicts and ensures proper operation
"""

import os
import sys
import time
import logging
import asyncio
import signal
import threading
import requests
from pathlib import Path
from datetime import datetime
import json

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

class UnifiedStartupManager:
    """مدير بدء التشغيل الموحد"""
    
    def __init__(self):
        self.startup_lock_file = "/tmp/bot_startup.lock"
        self.telegram_cleared = False
        self.environment_ready = False
        self.database_ready = False
        self.services_started = False
        
    def acquire_startup_lock(self):
        """الحصول على قفل بدء التشغيل"""
        try:
            if os.path.exists(self.startup_lock_file):
                with open(self.startup_lock_file, 'r') as f:
                    data = json.load(f)
                    old_pid = data.get('pid')
                    start_time = data.get('start_time')
                
                # فحص إذا كان العملية لا تزال تعمل
                if old_pid and self._is_process_running(old_pid):
                    logger.warning(f"⚠️ عملية بدء تشغيل أخرى نشطة (PID: {old_pid})")
                    return False
                else:
                    os.remove(self.startup_lock_file)
            
            # إنشاء قفل جديد
            lock_data = {
                'pid': os.getpid(),
                'start_time': datetime.now().isoformat(),
                'phase': 'startup'
            }
            
            with open(self.startup_lock_file, 'w') as f:
                json.dump(lock_data, f)
            
            logger.info(f"✅ تم الحصول على قفل بدء التشغيل (PID: {os.getpid()})")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على قفل بدء التشغيل: {e}")
            return False
    
    def _is_process_running(self, pid):
        """فحص إذا كان العملية لا تزال تعمل"""
        try:
            os.kill(pid, 0)
            return True
        except OSError:
            return False
    
    def release_startup_lock(self):
        """تحرير قفل بدء التشغيل"""
        try:
            if os.path.exists(self.startup_lock_file):
                os.remove(self.startup_lock_file)
                logger.info("🔓 تم تحرير قفل بدء التشغيل")
        except Exception as e:
            logger.error(f"❌ خطأ في تحرير قفل بدء التشغيل: {e}")
    
    def setup_environment(self):
        """إعداد البيئة الأساسية"""
        if self.environment_ready:
            return True
        
        logger.info("🔧 إعداد البيئة...")
        
        try:
            # متغيرات البيئة الأساسية
            env_vars = {
                'BOT_TOKEN': '**********************************************',
                'TELEGRAM_BOT_TOKEN': '**********************************************',
                'SUPABASE_URL': 'https://ytqxxodyecdeosnqoure.supabase.co',
                'SUPABASE_KEY': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4',
                'ADMIN_CHAT_ID': '7513880877',
                'USE_NGROK': 'false',
                'NGROK_ENABLED': 'false',
                'ENVIRONMENT': 'production',
                'DEBUG': 'false',
                'PYTHONUNBUFFERED': '1',
                'PYTHONIOENCODING': 'utf-8',
                'RENDER': 'true',
                'WEB_SERVER_URL': 'https://1c547fe5.sendaddons.pages.dev'
            }
            
            for key, value in env_vars.items():
                if not os.getenv(key):
                    os.environ[key] = str(value)
            
            # إنشاء المجلدات المطلوبة
            required_dirs = [
                'logs', 'temp', 'cache', 
                'security/logs', 'security/quarantine', 'security_logs'
            ]
            for dir_path in required_dirs:
                Path(dir_path).mkdir(parents=True, exist_ok=True)
            
            # إنشاء الملفات المطلوبة
            required_files = [
                'user_channels.json', 'all_users.json', 'user_feedback.json',
                'user_mods_status.json', 'user_blocked_mods.json', 'user_invitations.json',
                'user_subscriptions.json', 'user_feature_activation.json',
                'admin_settings.json', 'admin_processed_mods.json', 'pending_publication.json'
            ]
            
            for file_path in required_files:
                if not Path(file_path).exists():
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write('{}')
            
            self.environment_ready = True
            logger.info("✅ تم إعداد البيئة")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في إعداد البيئة: {e}")
            return False
    
    def clear_telegram_conflicts(self):
        """مسح تعارضات Telegram بطريقة شاملة"""
        if self.telegram_cleared:
            return True
        
        logger.info("🔧 مسح تعارضات Telegram...")
        
        try:
            bot_token = os.getenv('BOT_TOKEN')
            if not bot_token:
                logger.warning("⚠️ لم يتم العثور على BOT_TOKEN")
                return False
            
            # مسح webhook مع إعدادات شاملة
            try:
                response = requests.post(
                    f"https://api.telegram.org/bot{bot_token}/deleteWebhook",
                    json={"drop_pending_updates": True},
                    timeout=20
                )
                if response.status_code == 200 and response.json().get('ok'):
                    logger.info("✅ تم مسح webhook")
            except Exception as e:
                logger.warning(f"⚠️ تحذير في مسح webhook: {e}")
            
            # مسح جميع التحديثات المعلقة بطريقة متقدمة
            try:
                for attempt in range(5):  # 5 محاولات
                    response = requests.get(
                        f"https://api.telegram.org/bot{bot_token}/getUpdates",
                        params={"timeout": 1, "limit": 100},
                        timeout=20
                    )
                    
                    if response.status_code == 200:
                        result = response.json()
                        if result.get('ok') and result.get('result'):
                            updates = result['result']
                            if updates:
                                last_update_id = updates[-1]['update_id']
                                # مسح التحديثات
                                requests.get(
                                    f"https://api.telegram.org/bot{bot_token}/getUpdates",
                                    params={"offset": last_update_id + 1, "timeout": 1},
                                    timeout=15
                                )
                                logger.info(f"✅ تم مسح {len(updates)} تحديث معلق - المحاولة {attempt + 1}")
                            else:
                                logger.info("✅ لا توجد تحديثات معلقة")
                                break
                        else:
                            break
                    else:
                        logger.warning(f"⚠️ فشل في جلب التحديثات: {response.status_code}")
                        break
                    
                    # انتظار قصير بين المحاولات
                    time.sleep(2)
                    
            except Exception as e:
                logger.warning(f"⚠️ تحذير في مسح التحديثات: {e}")
            
            # انتظار إضافي للتأكد من تطبيق التغييرات
            time.sleep(5)
            
            self.telegram_cleared = True
            logger.info("✅ تم مسح تعارضات Telegram")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في مسح تعارضات Telegram: {e}")
            return False
    
    def check_database_connection(self):
        """فحص اتصال قاعدة البيانات"""
        if self.database_ready:
            return True
        
        logger.info("🔍 فحص اتصال قاعدة البيانات...")
        
        try:
            # استخدام مدير قاعدة البيانات المحسن إذا كان متوفراً
            try:
                from database_connection_manager import test_connection_with_details
                result = test_connection_with_details()
                self.database_ready = result
                return result
            except ImportError:
                # الطريقة التقليدية
                from supabase_client import test_supabase_connection
                result = test_supabase_connection()
                self.database_ready = result
                return result
                
        except Exception as e:
            logger.warning(f"⚠️ مشكلة في اتصال قاعدة البيانات: {e}")
            # لا نوقف التشغيل بسبب مشاكل قاعدة البيانات
            self.database_ready = False
            return False
    
    def start_health_services(self):
        """تشغيل خدمات الصحة"""
        if self.services_started:
            return True
        
        logger.info("🌐 تشغيل خدمات الصحة...")
        
        try:
            from flask import Flask, jsonify
            
            app = Flask(__name__)
            
            @app.route('/')
            def health():
                return jsonify({
                    "status": "healthy",
                    "service": "minecraft_mods_bot",
                    "timestamp": str(datetime.now()),
                    "platform": "render",
                    "pid": os.getpid(),
                    "environment_ready": self.environment_ready,
                    "telegram_cleared": self.telegram_cleared,
                    "database_ready": self.database_ready
                })
            
            @app.route('/health')
            def health_check():
                return jsonify({"status": "ok", "pid": os.getpid()})
            
            port = int(os.getenv('PORT', 10000))
            
            def run_server():
                app.run(host='0.0.0.0', port=port, debug=False, use_reloader=False)
            
            server_thread = threading.Thread(target=run_server, daemon=True)
            server_thread.start()
            
            self.services_started = True
            logger.info(f"✅ خدمات الصحة تعمل على المنفذ {port}")
            return True
            
        except Exception as e:
            logger.error(f"❌ فشل في تشغيل خدمات الصحة: {e}")
            return False
    
    async def start_bot(self):
        """تشغيل البوت الرئيسي"""
        logger.info("🤖 بدء تشغيل البوت...")
        
        try:
            # استيراد البوت الرئيسي
            import main
            
            # تشغيل البوت
            if hasattr(main, 'main') and asyncio.iscoroutinefunction(main.main):
                await main.main()
            elif hasattr(main, 'main'):
                main.main()
            else:
                logger.error("❌ لم يتم العثور على دالة main في البوت")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في تشغيل البوت: {e}")
            return False

# إنشاء instance عام
startup_manager = UnifiedStartupManager()

def setup_signal_handlers():
    """إعداد معالجات الإشارات"""
    def signal_handler(signum, frame):
        logger.info(f"📡 تم استلام إشارة {signum}، إغلاق آمن...")
        startup_manager.release_startup_lock()
        sys.exit(0)
    
    signal.signal(signal.SIGTERM, signal_handler)
    signal.signal(signal.SIGINT, signal_handler)

async def unified_startup():
    """بدء التشغيل الموحد"""
    logger.info("🚀 بدء التشغيل الموحد")
    logger.info("=" * 50)
    
    try:
        # إعداد معالجات الإشارات
        setup_signal_handlers()
        
        # الحصول على قفل بدء التشغيل
        if not startup_manager.acquire_startup_lock():
            logger.warning("⚠️ عملية بدء تشغيل أخرى نشطة، إنهاء هذا Instance")
            sys.exit(0)
        
        # المراحل المتسلسلة لبدء التشغيل
        steps = [
            ("إعداد البيئة", startup_manager.setup_environment),
            ("مسح تعارضات Telegram", startup_manager.clear_telegram_conflicts),
            ("فحص قاعدة البيانات", startup_manager.check_database_connection),
            ("تشغيل خدمات الصحة", startup_manager.start_health_services)
        ]
        
        for step_name, step_func in steps:
            logger.info(f"🔄 {step_name}...")
            success = step_func()
            if success:
                logger.info(f"✅ {step_name} - نجح")
            else:
                logger.warning(f"⚠️ {step_name} - فشل (سيتم المتابعة)")
        
        # تشغيل البوت
        logger.info("🚀 تشغيل البوت الرئيسي...")
        await startup_manager.start_bot()
        
    except KeyboardInterrupt:
        logger.info("⏹️ تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        logger.error(f"❌ خطأ في التشغيل الموحد: {e}")
        sys.exit(1)
    finally:
        startup_manager.release_startup_lock()

if __name__ == "__main__":
    try:
        asyncio.run(unified_startup())
    except KeyboardInterrupt:
        logger.info("⏹️ تم إيقاف البوت")
    except Exception as e:
        logger.error(f"❌ خطأ في التشغيل: {e}")
        sys.exit(1)
