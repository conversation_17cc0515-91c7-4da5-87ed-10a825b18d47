# ===================================================================
# عميل قاعدة بيانات المستخدمين - Supabase User Database Client
# ===================================================================
# هذا الملف يحتوي على جميع الدوال للتعامل مع قاعدة بيانات المستخدمين الجديدة
# بدلاً من التخزين المحلي في ملفات JSON
# ===================================================================

import os
import logging
import requests
import json
from typing import List, Dict, Optional, Any
from datetime import datetime, timezone
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

# إعداد التسجيل
logger = logging.getLogger(__name__)

# إعدادات قاعدة بيانات المستخدمين
USER_DB_URL = os.environ.get("USER_SUPABASE_URL", "")
USER_DB_KEY = os.environ.get("USER_SUPABASE_KEY", "")

# التحقق من وجود الإعدادات
if not USER_DB_URL or not USER_DB_KEY:
    logger.error("❌ إعدادات قاعدة بيانات المستخدمين مفقودة")
    logger.error("يرجى إعداد USER_SUPABASE_URL و USER_SUPABASE_KEY في متغيرات البيئة")

# إعداد headers للطلبات
USER_DB_HEADERS = {
    'apikey': USER_DB_KEY,
    'Authorization': f'Bearer {USER_DB_KEY}',
    'Content-Type': 'application/json',
    'Prefer': 'return=representation'
}

# ===================================================================
# دوال المستخدمين الأساسية (Users Functions)
# ===================================================================

def create_or_update_user(user_id: str, username: str = None, full_name: str = None, lang: str = "ar") -> bool:
    """إنشاء أو تحديث مستخدم"""
    try:
        user_data = {
            'user_id': str(user_id),
            'username': username,
            'full_name': full_name,
            'lang': lang,
            'last_activity': datetime.now(timezone.utc).isoformat(),
            'updated_at': datetime.now(timezone.utc).isoformat()
        }
        
        # محاولة التحديث أولاً
        url = f"{USER_DB_URL}/rest/v1/users"
        params = {'user_id': f'eq.{user_id}'}
        
        response = requests.patch(url, headers=USER_DB_HEADERS, json=user_data, params=params, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result:  # إذا تم التحديث
                logger.info(f"تم تحديث المستخدم {user_id}")
                return True
        
        # إذا لم يتم العثور على المستخدم، إنشاء جديد
        user_data['first_seen'] = datetime.now(timezone.utc).isoformat()
        user_data['created_at'] = datetime.now(timezone.utc).isoformat()
        
        response = requests.post(url, headers=USER_DB_HEADERS, json=user_data, timeout=10)
        
        if response.status_code == 201:
            logger.info(f"تم إنشاء المستخدم {user_id}")
            return True
        else:
            logger.error(f"فشل في إنشاء/تحديث المستخدم {user_id}: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"خطأ في إنشاء/تحديث المستخدم {user_id}: {e}")
        return False

def get_user(user_id: str) -> Optional[Dict]:
    """الحصول على بيانات مستخدم"""
    try:
        url = f"{USER_DB_URL}/rest/v1/users"
        params = {'user_id': f'eq.{user_id}'}
        
        response = requests.get(url, headers=USER_DB_HEADERS, params=params, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result:
                return result[0]
        
        return None
        
    except Exception as e:
        logger.error(f"خطأ في جلب بيانات المستخدم {user_id}: {e}")
        return None

def update_user_activity(user_id: str) -> bool:
    """تحديث آخر نشاط للمستخدم"""
    try:
        url = f"{USER_DB_URL}/rest/v1/users"
        params = {'user_id': f'eq.{user_id}'}
        data = {'last_activity': datetime.now(timezone.utc).isoformat()}
        
        response = requests.patch(url, headers=USER_DB_HEADERS, json=data, params=params, timeout=10)
        
        return response.status_code == 200
        
    except Exception as e:
        logger.error(f"خطأ في تحديث نشاط المستخدم {user_id}: {e}")
        return False

def get_all_users() -> List[Dict]:
    """الحصول على جميع المستخدمين"""
    try:
        url = f"{USER_DB_URL}/rest/v1/users"
        params = {'order': 'created_at.desc'}
        
        response = requests.get(url, headers=USER_DB_HEADERS, params=params, timeout=10)
        
        if response.status_code == 200:
            return response.json()
        
        return []
        
    except Exception as e:
        logger.error(f"خطأ في جلب جميع المستخدمين: {e}")
        return []

# ===================================================================
# دوال القنوات (Channels Functions)
# ===================================================================

def add_user_channel(user_id: str, channel_id: str, channel_name: str = None, 
                    is_default: bool = False, **kwargs) -> bool:
    """إضافة قناة للمستخدم"""
    try:
        # التأكد من وجود المستخدم أولاً
        if not get_user(user_id):
            create_or_update_user(user_id)
        
        channel_data = {
            'user_id': str(user_id),
            'channel_id': str(channel_id),
            'channel_name': channel_name,
            'is_default': is_default,
            'is_active': kwargs.get('active', True),
            'publish_interval': kwargs.get('publish_interval', 60),
            'preview_enabled': kwargs.get('preview_enabled', False),
            'message_format': kwargs.get('message_format', 'classic'),
            'channel_lang': kwargs.get('channel_lang', 'ar'),
            'created_at': datetime.now(timezone.utc).isoformat(),
            'updated_at': datetime.now(timezone.utc).isoformat()
        }
        
        url = f"{USER_DB_URL}/rest/v1/user_channels"
        response = requests.post(url, headers=USER_DB_HEADERS, json=channel_data, timeout=10)
        
        if response.status_code == 201:
            logger.info(f"تم إضافة القناة {channel_id} للمستخدم {user_id}")
            
            # إضافة فئات المودات إذا كانت موجودة
            mod_categories = kwargs.get('mod_categories', [])
            if mod_categories:
                add_channel_mod_categories(user_id, channel_id, mod_categories)
            
            # إضافة إصدارات ماين كرافت إذا كانت موجودة
            mc_versions = kwargs.get('mc_versions', [])
            if mc_versions:
                add_channel_mc_versions(user_id, channel_id, mc_versions)
            
            return True
        else:
            logger.error(f"فشل في إضافة القناة {channel_id} للمستخدم {user_id}: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"خطأ في إضافة القناة {channel_id} للمستخدم {user_id}: {e}")
        return False

def get_user_channels(user_id: str) -> List[Dict]:
    """الحصول على قنوات المستخدم"""
    try:
        url = f"{USER_DB_URL}/rest/v1/user_channels"
        params = {'user_id': f'eq.{user_id}', 'order': 'created_at.desc'}
        
        response = requests.get(url, headers=USER_DB_HEADERS, params=params, timeout=10)
        
        if response.status_code == 200:
            channels = response.json()
            
            # إضافة فئات المودات وإصدارات ماين كرافت لكل قناة
            for channel in channels:
                channel['mod_categories'] = get_channel_mod_categories(user_id, channel['channel_id'])
                channel['mc_versions'] = get_channel_mc_versions(user_id, channel['channel_id'])
            
            return channels
        
        return []
        
    except Exception as e:
        logger.error(f"خطأ في جلب قنوات المستخدم {user_id}: {e}")
        return []

def get_user_default_channel(user_id: str) -> Optional[str]:
    """الحصول على القناة الافتراضية للمستخدم"""
    try:
        url = f"{USER_DB_URL}/rest/v1/user_channels"
        params = {
            'user_id': f'eq.{user_id}',
            'is_default': 'eq.true',
            'is_active': 'eq.true',
            'limit': '1'
        }

        response = requests.get(url, headers=USER_DB_HEADERS, params=params, timeout=10)

        if response.status_code == 200:
            result = response.json()
            if result:
                return result[0]['channel_id']

        return None

    except Exception as e:
        logger.error(f"خطأ في جلب القناة الافتراضية للمستخدم {user_id}: {e}")
        return None

def update_channel_settings(user_id: str, channel_id: str, **kwargs) -> bool:
    """تحديث إعدادات القناة"""
    try:
        update_data = {
            'updated_at': datetime.now(timezone.utc).isoformat()
        }
        
        # إضافة الحقول المطلوب تحديثها
        allowed_fields = ['is_active', 'publish_interval', 'preview_enabled', 
                         'message_format', 'channel_lang', 'is_default', 'last_publish_time']
        
        for field in allowed_fields:
            if field in kwargs:
                update_data[field] = kwargs[field]
        
        url = f"{USER_DB_URL}/rest/v1/user_channels"
        params = {'user_id': f'eq.{user_id}', 'channel_id': f'eq.{channel_id}'}
        
        response = requests.patch(url, headers=USER_DB_HEADERS, json=update_data, params=params, timeout=10)
        
        if response.status_code == 200:
            logger.info(f"تم تحديث إعدادات القناة {channel_id} للمستخدم {user_id}")
            return True
        else:
            logger.error(f"فشل في تحديث إعدادات القناة: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"خطأ في تحديث إعدادات القناة {channel_id} للمستخدم {user_id}: {e}")
        return False

def delete_user_channel(user_id: str, channel_id: str) -> bool:
    """حذف قناة المستخدم"""
    try:
        url = f"{USER_DB_URL}/rest/v1/user_channels"
        params = {'user_id': f'eq.{user_id}', 'channel_id': f'eq.{channel_id}'}
        
        response = requests.delete(url, headers=USER_DB_HEADERS, params=params, timeout=10)
        
        if response.status_code == 204:
            logger.info(f"تم حذف القناة {channel_id} للمستخدم {user_id}")
            return True
        else:
            logger.error(f"فشل في حذف القناة: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"خطأ في حذف القناة {channel_id} للمستخدم {user_id}: {e}")
        return False

# ===================================================================
# دوال فئات المودات للقنوات (Channel Mod Categories)
# ===================================================================

def add_channel_mod_categories(user_id: str, channel_id: str, categories: List[str]) -> bool:
    """إضافة فئات المودات للقناة"""
    try:
        # حذف الفئات الموجودة أولاً
        delete_url = f"{USER_DB_URL}/rest/v1/channel_mod_categories"
        delete_params = {'user_id': f'eq.{user_id}', 'channel_id': f'eq.{channel_id}'}
        requests.delete(delete_url, headers=USER_DB_HEADERS, params=delete_params, timeout=10)
        
        # إضافة الفئات الجديدة
        for category in categories:
            category_data = {
                'user_id': str(user_id),
                'channel_id': str(channel_id),
                'category': category,
                'created_at': datetime.now(timezone.utc).isoformat()
            }
            
            url = f"{USER_DB_URL}/rest/v1/channel_mod_categories"
            response = requests.post(url, headers=USER_DB_HEADERS, json=category_data, timeout=10)
            
            if response.status_code != 201:
                logger.warning(f"فشل في إضافة فئة {category} للقناة {channel_id}")
        
        logger.info(f"تم إضافة فئات المودات للقناة {channel_id}")
        return True
        
    except Exception as e:
        logger.error(f"خطأ في إضافة فئات المودات للقناة {channel_id}: {e}")
        return False

def get_channel_mod_categories(user_id: str, channel_id: str) -> List[str]:
    """الحصول على فئات المودات للقناة"""
    try:
        url = f"{USER_DB_URL}/rest/v1/channel_mod_categories"
        params = {'user_id': f'eq.{user_id}', 'channel_id': f'eq.{channel_id}'}
        
        response = requests.get(url, headers=USER_DB_HEADERS, params=params, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            return [item['category'] for item in result]
        
        return []
        
    except Exception as e:
        logger.error(f"خطأ في جلب فئات المودات للقناة {channel_id}: {e}")
        return []

# ===================================================================
# دوال إصدارات ماين كرافت للقنوات (Channel MC Versions)
# ===================================================================

def add_channel_mc_versions(user_id: str, channel_id: str, versions: List[str]) -> bool:
    """إضافة إصدارات ماين كرافت للقناة"""
    try:
        # حذف الإصدارات الموجودة أولاً
        delete_url = f"{USER_DB_URL}/rest/v1/channel_mc_versions"
        delete_params = {'user_id': f'eq.{user_id}', 'channel_id': f'eq.{channel_id}'}
        requests.delete(delete_url, headers=USER_DB_HEADERS, params=delete_params, timeout=10)
        
        # إضافة الإصدارات الجديدة
        for version in versions:
            version_data = {
                'user_id': str(user_id),
                'channel_id': str(channel_id),
                'mc_version': version,
                'created_at': datetime.now(timezone.utc).isoformat()
            }
            
            url = f"{USER_DB_URL}/rest/v1/channel_mc_versions"
            response = requests.post(url, headers=USER_DB_HEADERS, json=version_data, timeout=10)
            
            if response.status_code != 201:
                logger.warning(f"فشل في إضافة إصدار {version} للقناة {channel_id}")
        
        logger.info(f"تم إضافة إصدارات ماين كرافت للقناة {channel_id}")
        return True
        
    except Exception as e:
        logger.error(f"خطأ في إضافة إصدارات ماين كرافت للقناة {channel_id}: {e}")
        return False

def get_channel_mc_versions(user_id: str, channel_id: str) -> List[str]:
    """الحصول على إصدارات ماين كرافت للقناة"""
    try:
        url = f"{USER_DB_URL}/rest/v1/channel_mc_versions"
        params = {'user_id': f'eq.{user_id}', 'channel_id': f'eq.{channel_id}'}
        
        response = requests.get(url, headers=USER_DB_HEADERS, params=params, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            return [item['mc_version'] for item in result]
        
        return []
        
    except Exception as e:
        logger.error(f"خطأ في جلب إصدارات ماين كرافت للقناة {channel_id}: {e}")
        return []

# ===================================================================
# دوال الاشتراكات (Subscriptions Functions)
# ===================================================================

def create_user_subscription(user_id: str, subscription_type: str = "free", 
                           end_date: str = None, **kwargs) -> bool:
    """إنشاء اشتراك للمستخدم"""
    try:
        # التأكد من وجود المستخدم أولاً
        if not get_user(user_id):
            create_or_update_user(user_id)
        
        subscription_data = {
            'user_id': str(user_id),
            'subscription_type': subscription_type,
            'start_date': datetime.now(timezone.utc).isoformat(),
            'end_date': end_date,
            'is_active': kwargs.get('is_active', True),
            'auto_renew': kwargs.get('auto_renew', False),
            'payment_method': kwargs.get('payment_method'),
            'created_at': datetime.now(timezone.utc).isoformat(),
            'updated_at': datetime.now(timezone.utc).isoformat()
        }
        
        url = f"{USER_DB_URL}/rest/v1/user_subscriptions"
        response = requests.post(url, headers=USER_DB_HEADERS, json=subscription_data, timeout=10)
        
        if response.status_code == 201:
            logger.info(f"تم إنشاء اشتراك {subscription_type} للمستخدم {user_id}")
            return True
        else:
            logger.error(f"فشل في إنشاء الاشتراك للمستخدم {user_id}: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"خطأ في إنشاء الاشتراك للمستخدم {user_id}: {e}")
        return False

def get_user_subscription(user_id: str) -> Optional[Dict]:
    """الحصول على اشتراك المستخدم النشط"""
    try:
        url = f"{USER_DB_URL}/rest/v1/user_subscriptions"
        params = {
            'user_id': f'eq.{user_id}',
            'is_active': 'eq.true',
            'order': 'created_at.desc',
            'limit': '1'
        }
        
        response = requests.get(url, headers=USER_DB_HEADERS, params=params, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result:
                return result[0]
        
        return None
        
    except Exception as e:
        logger.error(f"خطأ في جلب اشتراك المستخدم {user_id}: {e}")
        return None

# ===================================================================
# دوال تفعيل المميزات (Feature Activation Functions)
# ===================================================================

def set_user_feature(user_id: str, feature_name: str, is_enabled: bool,
                    expiry_date: str = None, activation_source: str = "admin") -> bool:
    """تعيين حالة ميزة للمستخدم"""
    try:
        # التأكد من وجود المستخدم أولاً
        if not get_user(user_id):
            create_or_update_user(user_id)

        feature_data = {
            'user_id': str(user_id),
            'feature_name': feature_name,
            'is_enabled': is_enabled,
            'activation_date': datetime.now(timezone.utc).isoformat() if is_enabled else None,
            'expiry_date': expiry_date,
            'activation_source': activation_source,
            'updated_at': datetime.now(timezone.utc).isoformat()
        }

        # محاولة التحديث أولاً
        url = f"{USER_DB_URL}/rest/v1/user_feature_activation"
        params = {'user_id': f'eq.{user_id}', 'feature_name': f'eq.{feature_name}'}

        response = requests.patch(url, headers=USER_DB_HEADERS, json=feature_data, params=params, timeout=10)

        if response.status_code == 200:
            result = response.json()
            if result:  # إذا تم التحديث
                logger.info(f"تم تحديث ميزة {feature_name} للمستخدم {user_id}")
                return True

        # إذا لم يتم العثور على الميزة، إنشاء جديدة
        feature_data['created_at'] = datetime.now(timezone.utc).isoformat()

        response = requests.post(url, headers=USER_DB_HEADERS, json=feature_data, timeout=10)

        if response.status_code == 201:
            logger.info(f"تم إنشاء ميزة {feature_name} للمستخدم {user_id}")
            return True
        else:
            logger.error(f"فشل في تعيين ميزة {feature_name} للمستخدم {user_id}: {response.status_code} - {response.text}")
            return False

    except Exception as e:
        logger.error(f"خطأ في تعيين ميزة {feature_name} للمستخدم {user_id}: {e}")
        return False

def is_user_feature_enabled(user_id: str, feature_name: str) -> bool:
    """التحقق من تفعيل ميزة للمستخدم"""
    try:
        url = f"{USER_DB_URL}/rest/v1/user_feature_activation"
        params = {
            'user_id': f'eq.{user_id}',
            'feature_name': f'eq.{feature_name}',
            'is_enabled': 'eq.true'
        }

        response = requests.get(url, headers=USER_DB_HEADERS, params=params, timeout=10)

        if response.status_code == 200:
            result = response.json()
            if result:
                # التحقق من انتهاء الصلاحية
                feature = result[0]
                if feature['expiry_date']:
                    from datetime import datetime, timezone
                    expiry = datetime.fromisoformat(feature['expiry_date'].replace('Z', '+00:00'))
                    if expiry <= datetime.now(timezone.utc):
                        return False
                return True

        return False

    except Exception as e:
        logger.error(f"خطأ في التحقق من ميزة {feature_name} للمستخدم {user_id}: {e}")
        return False

def get_user_features(user_id: str) -> Dict[str, bool]:
    """الحصول على جميع مميزات المستخدم"""
    try:
        url = f"{USER_DB_URL}/rest/v1/user_feature_activation"
        params = {'user_id': f'eq.{user_id}'}

        response = requests.get(url, headers=USER_DB_HEADERS, params=params, timeout=10)

        if response.status_code == 200:
            features = response.json()
            result = {}

            for feature in features:
                # التحقق من انتهاء الصلاحية
                is_active = feature['is_enabled']
                if feature['expiry_date']:
                    expiry = datetime.fromisoformat(feature['expiry_date'].replace('Z', '+00:00'))
                    if expiry <= datetime.now(timezone.utc):
                        is_active = False

                result[feature['feature_name']] = is_active

            return result

        return {}

    except Exception as e:
        logger.error(f"خطأ في جلب مميزات المستخدم {user_id}: {e}")
        return {}

# ===================================================================
# دوال التقييمات (Feedback Functions)
# ===================================================================

def save_user_feedback(user_id: str, mod_id: int, feedback_type: str,
                      rating: int = None, comment: str = None) -> bool:
    """حفظ تقييم المستخدم"""
    try:
        # التأكد من وجود المستخدم أولاً
        if not get_user(user_id):
            create_or_update_user(user_id)

        feedback_data = {
            'user_id': str(user_id),
            'mod_id': mod_id,
            'feedback_type': feedback_type,
            'rating': rating,
            'comment': comment,
            'created_at': datetime.now(timezone.utc).isoformat()
        }

        url = f"{USER_DB_URL}/rest/v1/user_feedback"
        response = requests.post(url, headers=USER_DB_HEADERS, json=feedback_data, timeout=10)

        if response.status_code == 201:
            logger.info(f"تم حفظ تقييم المستخدم {user_id} للمود {mod_id}")
            return True
        else:
            # محاولة التحديث إذا كان التقييم موجود
            params = {'user_id': f'eq.{user_id}', 'mod_id': f'eq.{mod_id}'}
            response = requests.patch(url, headers=USER_DB_HEADERS, json=feedback_data, params=params, timeout=10)

            if response.status_code == 200:
                logger.info(f"تم تحديث تقييم المستخدم {user_id} للمود {mod_id}")
                return True
            else:
                logger.error(f"فشل في حفظ تقييم المستخدم {user_id}: {response.status_code} - {response.text}")
                return False

    except Exception as e:
        logger.error(f"خطأ في حفظ تقييم المستخدم {user_id} للمود {mod_id}: {e}")
        return False

def get_user_feedback(user_id: str, mod_id: int) -> Optional[Dict]:
    """الحصول على تقييم المستخدم لمود معين"""
    try:
        url = f"{USER_DB_URL}/rest/v1/user_feedback"
        params = {'user_id': f'eq.{user_id}', 'mod_id': f'eq.{mod_id}'}

        response = requests.get(url, headers=USER_DB_HEADERS, params=params, timeout=10)

        if response.status_code == 200:
            result = response.json()
            if result:
                return result[0]

        return None

    except Exception as e:
        logger.error(f"خطأ في جلب تقييم المستخدم {user_id} للمود {mod_id}: {e}")
        return None

# ===================================================================
# دوال حالة المودات (Mods Status Functions)
# ===================================================================

def save_user_mod_status(user_id: str, mod_id: int, status: str = "published",
                        channel_id: str = None) -> bool:
    """حفظ حالة مود للمستخدم"""
    try:
        # التأكد من وجود المستخدم أولاً
        if not get_user(user_id):
            create_or_update_user(user_id)

        status_data = {
            'user_id': str(user_id),
            'mod_id': mod_id,
            'status': status,
            'channel_id': channel_id,
            'published_at': datetime.now(timezone.utc).isoformat()
        }

        url = f"{USER_DB_URL}/rest/v1/user_mods_status"
        response = requests.post(url, headers=USER_DB_HEADERS, json=status_data, timeout=10)

        if response.status_code == 201:
            logger.info(f"تم حفظ حالة المود {mod_id} للمستخدم {user_id}")
            return True
        else:
            # محاولة التحديث إذا كانت الحالة موجودة
            params = {'user_id': f'eq.{user_id}', 'mod_id': f'eq.{mod_id}'}
            response = requests.patch(url, headers=USER_DB_HEADERS, json=status_data, params=params, timeout=10)

            if response.status_code == 200:
                logger.info(f"تم تحديث حالة المود {mod_id} للمستخدم {user_id}")
                return True
            else:
                logger.error(f"فشل في حفظ حالة المود {mod_id} للمستخدم {user_id}: {response.status_code} - {response.text}")
                return False

    except Exception as e:
        logger.error(f"خطأ في حفظ حالة المود {mod_id} للمستخدم {user_id}: {e}")
        return False

def get_user_published_mods(user_id: str) -> List[int]:
    """الحصول على قائمة المودات المنشورة للمستخدم"""
    try:
        url = f"{USER_DB_URL}/rest/v1/user_mods_status"
        params = {'user_id': f'eq.{user_id}', 'status': 'eq.published'}

        response = requests.get(url, headers=USER_DB_HEADERS, params=params, timeout=10)

        if response.status_code == 200:
            result = response.json()
            return [item['mod_id'] for item in result]

        return []

    except Exception as e:
        logger.error(f"خطأ في جلب المودات المنشورة للمستخدم {user_id}: {e}")
        return []

def is_mod_published_for_user(user_id: str, mod_id: int) -> bool:
    """التحقق من نشر مود للمستخدم"""
    try:
        url = f"{USER_DB_URL}/rest/v1/user_mods_status"
        params = {'user_id': f'eq.{user_id}', 'mod_id': f'eq.{mod_id}', 'status': 'eq.published'}

        response = requests.get(url, headers=USER_DB_HEADERS, params=params, timeout=10)

        if response.status_code == 200:
            result = response.json()
            return len(result) > 0

        return False

    except Exception as e:
        logger.error(f"خطأ في التحقق من نشر المود {mod_id} للمستخدم {user_id}: {e}")
        return False

# ===================================================================
# اختبار الاتصال
# ===================================================================

# ===================================================================
# دوال المودات المحظورة (Blocked Mods Functions)
# ===================================================================

def block_mod_for_user(user_id: str, mod_id: int, reason: str = None) -> bool:
    """حظر مود للمستخدم"""
    try:
        # التأكد من وجود المستخدم أولاً
        if not get_user(user_id):
            create_or_update_user(user_id)

        block_data = {
            'user_id': str(user_id),
            'mod_id': mod_id,
            'reason': reason,
            'blocked_at': datetime.now(timezone.utc).isoformat()
        }

        url = f"{USER_DB_URL}/rest/v1/user_blocked_mods"
        response = requests.post(url, headers=USER_DB_HEADERS, json=block_data, timeout=10)

        if response.status_code == 201:
            logger.info(f"تم حظر المود {mod_id} للمستخدم {user_id}")
            return True
        else:
            logger.error(f"فشل في حظر المود {mod_id} للمستخدم {user_id}: {response.status_code} - {response.text}")
            return False

    except Exception as e:
        logger.error(f"خطأ في حظر المود {mod_id} للمستخدم {user_id}: {e}")
        return False

def unblock_mod_for_user(user_id: str, mod_id: int) -> bool:
    """إلغاء حظر مود للمستخدم"""
    try:
        url = f"{USER_DB_URL}/rest/v1/user_blocked_mods"
        params = {'user_id': f'eq.{user_id}', 'mod_id': f'eq.{mod_id}'}

        response = requests.delete(url, headers=USER_DB_HEADERS, params=params, timeout=10)

        if response.status_code == 204:
            logger.info(f"تم إلغاء حظر المود {mod_id} للمستخدم {user_id}")
            return True
        else:
            logger.error(f"فشل في إلغاء حظر المود {mod_id} للمستخدم {user_id}: {response.status_code}")
            return False

    except Exception as e:
        logger.error(f"خطأ في إلغاء حظر المود {mod_id} للمستخدم {user_id}: {e}")
        return False

def get_user_blocked_mods(user_id: str) -> List[int]:
    """الحصول على قائمة المودات المحظورة للمستخدم"""
    try:
        url = f"{USER_DB_URL}/rest/v1/user_blocked_mods"
        params = {'user_id': f'eq.{user_id}'}

        response = requests.get(url, headers=USER_DB_HEADERS, params=params, timeout=10)

        if response.status_code == 200:
            result = response.json()
            return [item['mod_id'] for item in result]

        return []

    except Exception as e:
        logger.error(f"خطأ في جلب المودات المحظورة للمستخدم {user_id}: {e}")
        return []

def is_mod_blocked_for_user(user_id: str, mod_id: int) -> bool:
    """التحقق من حظر مود للمستخدم"""
    try:
        url = f"{USER_DB_URL}/rest/v1/user_blocked_mods"
        params = {'user_id': f'eq.{user_id}', 'mod_id': f'eq.{mod_id}'}

        response = requests.get(url, headers=USER_DB_HEADERS, params=params, timeout=10)

        if response.status_code == 200:
            result = response.json()
            return len(result) > 0

        return False

    except Exception as e:
        logger.error(f"خطأ في التحقق من حظر المود {mod_id} للمستخدم {user_id}: {e}")
        return False

# ===================================================================
# دوال نظام الدعوات (Invitations Functions)
# ===================================================================

def create_invitation(inviter_user_id: str, invitation_code: str = None,
                     expires_days: int = 30) -> Optional[str]:
    """إنشاء دعوة جديدة"""
    try:
        # التأكد من وجود المستخدم أولاً
        if not get_user(inviter_user_id):
            create_or_update_user(inviter_user_id)

        # إنشاء كود دعوة إذا لم يتم تمريره
        if not invitation_code:
            import uuid
            invitation_code = str(uuid.uuid4())[:8].upper()

        # حساب تاريخ انتهاء الصلاحية
        from datetime import timedelta
        expires_at = datetime.now(timezone.utc) + timedelta(days=expires_days)

        invitation_data = {
            'inviter_user_id': str(inviter_user_id),
            'invitation_code': invitation_code,
            'status': 'pending',
            'reward_claimed': False,
            'created_at': datetime.now(timezone.utc).isoformat(),
            'expires_at': expires_at.isoformat()
        }

        url = f"{USER_DB_URL}/rest/v1/user_invitations"
        response = requests.post(url, headers=USER_DB_HEADERS, json=invitation_data, timeout=10)

        if response.status_code == 201:
            logger.info(f"تم إنشاء دعوة {invitation_code} للمستخدم {inviter_user_id}")
            return invitation_code
        else:
            logger.error(f"فشل في إنشاء الدعوة للمستخدم {inviter_user_id}: {response.status_code} - {response.text}")
            return None

    except Exception as e:
        logger.error(f"خطأ في إنشاء الدعوة للمستخدم {inviter_user_id}: {e}")
        return None

def accept_invitation(invitation_code: str, invited_user_id: str) -> bool:
    """قبول دعوة"""
    try:
        # التأكد من وجود المستخدم المدعو أولاً
        if not get_user(invited_user_id):
            create_or_update_user(invited_user_id)

        # البحث عن الدعوة
        url = f"{USER_DB_URL}/rest/v1/user_invitations"
        params = {'invitation_code': f'eq.{invitation_code}', 'status': 'eq.pending'}

        response = requests.get(url, headers=USER_DB_HEADERS, params=params, timeout=10)

        if response.status_code == 200:
            invitations = response.json()
            if not invitations:
                logger.warning(f"لم يتم العثور على دعوة صالحة بالكود {invitation_code}")
                return False

            invitation = invitations[0]

            # التحقق من انتهاء الصلاحية
            if invitation['expires_at']:
                expires_at = datetime.fromisoformat(invitation['expires_at'].replace('Z', '+00:00'))
                if expires_at <= datetime.now(timezone.utc):
                    logger.warning(f"انتهت صلاحية الدعوة {invitation_code}")
                    return False

            # تحديث الدعوة
            update_data = {
                'invited_user_id': str(invited_user_id),
                'status': 'accepted',
                'accepted_at': datetime.now(timezone.utc).isoformat()
            }

            params = {'invitation_code': f'eq.{invitation_code}'}
            response = requests.patch(url, headers=USER_DB_HEADERS, json=update_data, params=params, timeout=10)

            if response.status_code == 200:
                logger.info(f"تم قبول الدعوة {invitation_code} من قبل المستخدم {invited_user_id}")
                return True
            else:
                logger.error(f"فشل في تحديث الدعوة {invitation_code}: {response.status_code}")
                return False

        return False

    except Exception as e:
        logger.error(f"خطأ في قبول الدعوة {invitation_code}: {e}")
        return False

def get_user_invitations(user_id: str) -> List[Dict]:
    """الحصول على دعوات المستخدم"""
    try:
        url = f"{USER_DB_URL}/rest/v1/user_invitations"
        params = {'inviter_user_id': f'eq.{user_id}', 'order': 'created_at.desc'}

        response = requests.get(url, headers=USER_DB_HEADERS, params=params, timeout=10)

        if response.status_code == 200:
            return response.json()

        return []

    except Exception as e:
        logger.error(f"خطأ في جلب دعوات المستخدم {user_id}: {e}")
        return []

# ===================================================================
# دوال إعدادات المسؤول (Admin Settings Functions)
# ===================================================================

def get_admin_setting(setting_key: str) -> Any:
    """الحصول على إعداد المسؤول"""
    try:
        url = f"{USER_DB_URL}/rest/v1/admin_settings"
        params = {'setting_key': f'eq.{setting_key}'}

        response = requests.get(url, headers=USER_DB_HEADERS, params=params, timeout=10)

        if response.status_code == 200:
            result = response.json()
            if result:
                try:
                    # محاولة تحويل JSON إذا كان ممكناً
                    return json.loads(result[0]['setting_value'])
                except (json.JSONDecodeError, TypeError):
                    # إذا لم يكن JSON صالح، استخدم القيمة كما هي
                    return result[0]['setting_value']

        return None

    except Exception as e:
        logger.error(f"خطأ في جلب إعداد المسؤول {setting_key}: {e}")
        return None

def set_admin_setting(setting_key: str, setting_value: Any, description: str = None) -> bool:
    """تعيين إعداد المسؤول"""
    try:
        setting_data = {
            'setting_key': setting_key,
            'setting_value': json.dumps(setting_value) if not isinstance(setting_value, str) else setting_value,
            'description': description,
            'updated_at': datetime.now(timezone.utc).isoformat()
        }

        # محاولة التحديث أولاً
        url = f"{USER_DB_URL}/rest/v1/admin_settings"
        params = {'setting_key': f'eq.{setting_key}'}

        response = requests.patch(url, headers=USER_DB_HEADERS, json=setting_data, params=params, timeout=10)

        if response.status_code == 200:
            result = response.json()
            if result:  # إذا تم التحديث
                logger.info(f"تم تحديث إعداد المسؤول {setting_key}")
                return True

        # إذا لم يتم العثور على الإعداد، إنشاء جديد
        setting_data['created_at'] = datetime.now(timezone.utc).isoformat()

        response = requests.post(url, headers=USER_DB_HEADERS, json=setting_data, timeout=10)

        if response.status_code == 201:
            logger.info(f"تم إنشاء إعداد المسؤول {setting_key}")
            return True
        else:
            logger.error(f"فشل في تعيين إعداد المسؤول {setting_key}: {response.status_code} - {response.text}")
            return False

    except Exception as e:
        logger.error(f"خطأ في تعيين إعداد المسؤول {setting_key}: {e}")
        return False

def get_all_admin_settings() -> Dict[str, Any]:
    """الحصول على جميع إعدادات المسؤول"""
    try:
        url = f"{USER_DB_URL}/rest/v1/admin_settings"

        response = requests.get(url, headers=USER_DB_HEADERS, timeout=10)

        if response.status_code == 200:
            settings = response.json()
            result = {}

            for setting in settings:
                try:
                    # محاولة تحويل JSON إذا كان ممكناً
                    value = json.loads(setting['setting_value'])
                except (json.JSONDecodeError, TypeError):
                    # إذا لم يكن JSON صالح، استخدم القيمة كما هي
                    value = setting['setting_value']

                result[setting['setting_key']] = value

            return result

        return {}

    except Exception as e:
        logger.error(f"خطأ في جلب جميع إعدادات المسؤول: {e}")
        return {}

# ===================================================================
# اختبار الاتصال
# ===================================================================

def test_user_database_connection() -> bool:
    """اختبار الاتصال مع قاعدة بيانات المستخدمين"""
    try:
        if not USER_DB_URL or not USER_DB_KEY:
            logger.error("❌ إعدادات قاعدة بيانات المستخدمين مفقودة")
            return False

        url = f"{USER_DB_URL}/rest/v1/users"
        params = {'limit': '1'}

        response = requests.get(url, headers=USER_DB_HEADERS, params=params, timeout=10)

        if response.status_code == 200:
            logger.info("✅ تم الاتصال بقاعدة بيانات المستخدمين بنجاح")
            return True
        else:
            logger.error(f"❌ فشل الاتصال بقاعدة بيانات المستخدمين: {response.status_code}")
            return False

    except Exception as e:
        logger.error(f"❌ خطأ في اختبار الاتصال بقاعدة بيانات المستخدمين: {e}")
        return False
