#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح مشاكل الشبكة في بيئة Render
هذا الملف يحل مشاكل DNS والاتصال في بيئات الاستضافة السحابية
"""

import os
import sys
import logging
import urllib.request
import urllib.error
import socket
from typing import Dict, Any, Optional

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class RenderNetworkFixer:
    """فئة لإصلاح مشاكل الشبكة في بيئة Render"""
    
    def __init__(self):
        self.is_cloud_environment = self._detect_cloud_environment()
        logger.info(f"🌐 بيئة التشغيل: {'سحابية' if self.is_cloud_environment else 'محلية'}")
    
    def _detect_cloud_environment(self) -> bool:
        """كشف بيئة الاستضافة السحابية"""
        cloud_vars = ['RENDER', 'HEROKU', 'RAILWAY', 'VERCEL', 'NETLIFY']
        return any(os.getenv(var) for var in cloud_vars)
    
    def check_basic_connectivity(self) -> bool:
        """فحص الاتصال الأساسي"""
        logger.info("🔍 فحص الاتصال الأساسي...")
        
        if self.is_cloud_environment:
            return self._check_cloud_connectivity()
        else:
            return self._check_local_connectivity()
    
    def _check_cloud_connectivity(self) -> bool:
        """فحص الاتصال في البيئة السحابية"""
        test_urls = [
            'https://httpbin.org/get',
            'https://api.github.com',
            'https://www.google.com',
            'https://1.1.1.1'
        ]
        
        for url in test_urls:
            try:
                req = urllib.request.Request(url, headers={
                    'User-Agent': 'Mozilla/5.0 (compatible; RenderBot/1.0)'
                })
                with urllib.request.urlopen(req, timeout=15) as response:
                    if response.status == 200:
                        logger.info(f"✅ نجح الاتصال مع: {url}")
                        return True
            except Exception as e:
                logger.warning(f"⚠️ فشل الاتصال مع {url}: {e}")
                continue
        
        logger.error("❌ فشل في جميع اختبارات الاتصال")
        return False
    
    def _check_local_connectivity(self) -> bool:
        """فحص الاتصال في البيئة المحلية"""
        dns_servers = [
            ('8.8.8.8', 53),
            ('1.1.1.1', 53),
            ('208.67.222.222', 53)
        ]
        
        for server, port in dns_servers:
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(10)
                result = sock.connect_ex((server, port))
                sock.close()
                
                if result == 0:
                    logger.info(f"✅ نجح الاتصال مع DNS: {server}")
                    return True
            except Exception as e:
                logger.warning(f"⚠️ فشل الاتصال مع {server}: {e}")
                continue
        
        logger.error("❌ فشل في جميع اختبارات DNS")
        return False
    
    def check_telegram_connectivity(self) -> bool:
        """فحص الاتصال مع Telegram"""
        logger.info("🔍 فحص الاتصال مع Telegram...")
        
        if self.is_cloud_environment:
            return self._check_telegram_cloud()
        else:
            return self._check_telegram_local()
    
    def _check_telegram_cloud(self) -> bool:
        """فحص Telegram في البيئة السحابية"""
        telegram_urls = [
            'https://api.telegram.org',
            'https://core.telegram.org'
        ]
        
        for url in telegram_urls:
            try:
                req = urllib.request.Request(url, headers={
                    'User-Agent': 'TelegramBot/1.0'
                })
                with urllib.request.urlopen(req, timeout=15) as response:
                    # 200, 401, 404 كلها تعني أن الخادم يرد
                    if response.status in [200, 401, 404]:
                        logger.info(f"✅ نجح الاتصال مع Telegram: {url}")
                        return True
            except Exception as e:
                logger.warning(f"⚠️ فشل الاتصال مع {url}: {e}")
                continue
        
        logger.warning("⚠️ لم يتم التأكد من اتصال Telegram")
        return False
    
    def _check_telegram_local(self) -> bool:
        """فحص Telegram في البيئة المحلية"""
        telegram_servers = [
            ('api.telegram.org', 443),
            ('api.telegram.org', 80)
        ]
        
        for server, port in telegram_servers:
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(15)
                result = sock.connect_ex((server, port))
                sock.close()
                
                if result == 0:
                    logger.info(f"✅ نجح الاتصال مع Telegram: {server}:{port}")
                    return True
            except Exception as e:
                logger.warning(f"⚠️ فشل الاتصال مع {server}:{port}: {e}")
                continue
        
        logger.warning("⚠️ لم يتم التأكد من اتصال Telegram")
        return False
    
    def apply_network_fixes(self) -> bool:
        """تطبيق إصلاحات الشبكة"""
        logger.info("🔧 تطبيق إصلاحات الشبكة...")
        
        try:
            # تعيين timeout افتراضي
            socket.setdefaulttimeout(30)
            
            # في البيئة السحابية، نطبق إصلاحات مختلفة
            if self.is_cloud_environment:
                self._apply_cloud_fixes()
            else:
                self._apply_local_fixes()
            
            logger.info("✅ تم تطبيق إصلاحات الشبكة")
            return True
            
        except Exception as e:
            logger.error(f"❌ فشل في تطبيق إصلاحات الشبكة: {e}")
            return False
    
    def _apply_cloud_fixes(self):
        """إصلاحات خاصة بالبيئة السحابية"""
        # تعيين متغيرات البيئة للشبكة
        os.environ.setdefault('PYTHONHTTPSVERIFY', '1')
        os.environ.setdefault('REQUESTS_CA_BUNDLE', '')
        
        # تفعيل IPv4 فقط
        original_getaddrinfo = socket.getaddrinfo
        def ipv4_getaddrinfo(host, port, family=0, type=0, proto=0, flags=0):
            return original_getaddrinfo(host, port, socket.AF_INET, type, proto, flags)
        socket.getaddrinfo = ipv4_getaddrinfo
        
        logger.info("✅ تم تطبيق إصلاحات البيئة السحابية")
    
    def _apply_local_fixes(self):
        """إصلاحات خاصة بالبيئة المحلية"""
        # تفعيل IPv4 فقط
        original_getaddrinfo = socket.getaddrinfo
        def ipv4_getaddrinfo(host, port, family=0, type=0, proto=0, flags=0):
            return original_getaddrinfo(host, port, socket.AF_INET, type, proto, flags)
        socket.getaddrinfo = ipv4_getaddrinfo
        
        logger.info("✅ تم تطبيق إصلاحات البيئة المحلية")
    
    def run_full_diagnosis(self) -> Dict[str, Any]:
        """تشغيل تشخيص كامل للشبكة"""
        logger.info("🔍 بدء التشخيص الكامل للشبكة...")
        
        results = {
            'environment': 'cloud' if self.is_cloud_environment else 'local',
            'basic_connectivity': False,
            'telegram_connectivity': False,
            'fixes_applied': False,
            'recommendations': []
        }
        
        # تطبيق الإصلاحات أولاً
        results['fixes_applied'] = self.apply_network_fixes()
        
        # فحص الاتصال الأساسي
        results['basic_connectivity'] = self.check_basic_connectivity()
        
        # فحص اتصال Telegram
        results['telegram_connectivity'] = self.check_telegram_connectivity()
        
        # إضافة التوصيات
        if not results['basic_connectivity']:
            results['recommendations'].append("فحص إعدادات الشبكة والجدار الناري")
        
        if not results['telegram_connectivity']:
            results['recommendations'].append("التأكد من عدم حجب Telegram في الشبكة")
        
        if results['basic_connectivity'] and results['telegram_connectivity']:
            results['recommendations'].append("الشبكة تعمل بشكل طبيعي")
        
        return results

def main():
    """الدالة الرئيسية"""
    print("🔧 أداة إصلاح مشاكل الشبكة لبيئة Render")
    print("=" * 50)
    
    fixer = RenderNetworkFixer()
    results = fixer.run_full_diagnosis()
    
    print("\n📊 نتائج التشخيص:")
    print(f"   البيئة: {results['environment']}")
    print(f"   الاتصال الأساسي: {'✅' if results['basic_connectivity'] else '❌'}")
    print(f"   اتصال Telegram: {'✅' if results['telegram_connectivity'] else '❌'}")
    print(f"   تطبيق الإصلاحات: {'✅' if results['fixes_applied'] else '❌'}")
    
    if results['recommendations']:
        print("\n💡 التوصيات:")
        for rec in results['recommendations']:
            print(f"   • {rec}")
    
    # تحديد حالة الخروج
    if results['basic_connectivity'] and results['telegram_connectivity']:
        print("\n🎉 جميع الاختبارات نجحت!")
        sys.exit(0)
    else:
        print("\n⚠️ بعض الاختبارات فشلت، لكن البوت قد يعمل مع إعدادات محسنة")
        sys.exit(0)  # لا نوقف البوت، بل نتابع

if __name__ == "__main__":
    main()
