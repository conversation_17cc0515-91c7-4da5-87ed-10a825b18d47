#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إعداد البوت للاستضافة المجانية
Deployment Configuration for Free Hosting
"""

import os
import sys
import logging
from pathlib import Path

# إعداد المسارات
BASE_DIR = Path(__file__).parent
LOG_DIR = BASE_DIR / "logs"
LOG_DIR.mkdir(exist_ok=True)

# إعداد التسجيل للاستضافة
def setup_production_logging():
    """إعداد التسجيل للإنتاج"""
    log_level = os.environ.get('LOG_LEVEL', 'INFO').upper()
    
    logging.basicConfig(
        level=getattr(logging, log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),  # للاستضافة المجانية
            logging.FileHandler(LOG_DIR / "bot.log", encoding='utf-8')
        ]
    )
    
    # تقليل مستوى تسجيل المكتبات الخارجية
    logging.getLogger('httpx').setLevel(logging.WARNING)
    logging.getLogger('telegram').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)

# فحص متغيرات البيئة المطلوبة
def check_environment():
    """فحص متغيرات البيئة المطلوبة"""
    required_vars = [
        'BOT_TOKEN',
        'ADMIN_CHAT_ID',
        'SUPABASE_URL',
        'SUPABASE_KEY'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.environ.get(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ متغيرات البيئة المفقودة: {', '.join(missing_vars)}")
        print("🔧 تأكد من إضافة هذه المتغيرات في إعدادات الاستضافة")
        return False
    
    print("✅ جميع متغيرات البيئة موجودة")
    return True

# إعداد البوت للاستضافة المجانية
def setup_for_hosting():
    """إعداد البوت للاستضافة المجانية"""
    
    # تحديد نوع الاستضافة
    hosting_type = "unknown"
    
    if os.environ.get('RAILWAY_ENVIRONMENT'):
        hosting_type = "railway"
    elif os.environ.get('RENDER'):
        hosting_type = "render"
    elif os.environ.get('DYNO'):
        hosting_type = "heroku"
    elif os.environ.get('KOYEB_APP_NAME'):
        hosting_type = "koyeb"
    
    print(f"🚀 تشغيل البوت على: {hosting_type}")
    
    # إعداد المنفذ للاستضافة
    port = int(os.environ.get('PORT', 5000))
    os.environ.setdefault('WEB_SERVER_PORT', str(port))
    
    # إعداد البيئة
    os.environ.setdefault('ENVIRONMENT', 'production')
    os.environ.setdefault('DEBUG', 'false')
    
    return hosting_type

# دالة ping لمنع النوم (للاستضافات التي تنام)
def create_ping_server():
    """إنشاء خادم ping لمنع النوم"""
    from flask import Flask
    import threading
    
    app = Flask(__name__)
    
    @app.route('/')
    def home():
        return {
            "status": "alive",
            "bot": "Minecraft Mods Bot",
            "message": "البوت يعمل بشكل طبيعي"
        }
    
    @app.route('/ping')
    def ping():
        return {"status": "pong", "timestamp": str(datetime.now())}
    
    @app.route('/health')
    def health():
        return {
            "status": "healthy",
            "uptime": "running",
            "environment": os.environ.get('ENVIRONMENT', 'development')
        }
    
    def run_flask():
        port = int(os.environ.get('PORT', 5000))
        app.run(host='0.0.0.0', port=port, debug=False)
    
    # تشغيل Flask في thread منفصل
    flask_thread = threading.Thread(target=run_flask, daemon=True)
    flask_thread.start()
    
    print(f"🌐 خادم Ping يعمل على المنفذ {os.environ.get('PORT', 5000)}")

# دالة الإعداد الرئيسية
def main():
    """الدالة الرئيسية للإعداد"""
    print("🔧 إعداد البوت للاستضافة...")
    
    # فحص متغيرات البيئة
    if not check_environment():
        sys.exit(1)
    
    # إعداد التسجيل
    setup_production_logging()
    
    # إعداد الاستضافة
    hosting_type = setup_for_hosting()
    
    # إنشاء خادم ping للاستضافات التي تنام
    if hosting_type in ['render', 'heroku']:
        create_ping_server()
    
    print("✅ تم إعداد البوت بنجاح للاستضافة")
    return True

if __name__ == "__main__":
    main()
