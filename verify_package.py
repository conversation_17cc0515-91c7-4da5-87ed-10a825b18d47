#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت التحقق من اكتمال حزمة البوت
Bot Package Verification Script
"""

import os
import sys
import logging

# إعداد السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_essential_files():
    """فحص الملفات الأساسية"""
    essential_files = [
        "main.py",
        "supabase_client.py",
        "web_server.py", 
        "telegram_web_app.py",
        "network_config.py",
        "requirements.txt",
        ".env.example",
        "setup.py",
        "README.md",
        "QUICK_DEPLOY.md",
        "HOSTING_GUIDES.md"
    ]
    
    missing_files = []
    for file_name in essential_files:
        if not os.path.exists(file_name):
            missing_files.append(file_name)
        else:
            logger.info(f"✅ {file_name}")
    
    if missing_files:
        logger.error(f"❌ ملفات مفقودة: {missing_files}")
        return False
    
    logger.info("✅ جميع الملفات الأساسية موجودة")
    return True

def check_directories():
    """فحص المجلدات المطلوبة"""
    required_dirs = [
        "cloudflare_ready",
        "logs", 
        "temp",
        "user_customizations"
    ]
    
    missing_dirs = []
    for dir_name in required_dirs:
        if not os.path.exists(dir_name):
            missing_dirs.append(dir_name)
        else:
            logger.info(f"✅ {dir_name}/")
    
    if missing_dirs:
        logger.error(f"❌ مجلدات مفقودة: {missing_dirs}")
        return False
    
    logger.info("✅ جميع المجلدات موجودة")
    return True

def check_cloudflare_files():
    """فحص ملفات صفحة التحميل"""
    cloudflare_files = [
        "cloudflare_ready/index.html",
        "cloudflare_ready/script.js",
        "cloudflare_ready/style.css",
        "cloudflare_ready/style-templates.css",
        "cloudflare_ready/_headers",
        "cloudflare_ready/_redirects",
        "cloudflare_ready/robots.txt"
    ]
    
    missing_files = []
    for file_name in cloudflare_files:
        if not os.path.exists(file_name):
            missing_files.append(file_name)
        else:
            logger.info(f"✅ {file_name}")
    
    if missing_files:
        logger.error(f"❌ ملفات Cloudflare مفقودة: {missing_files}")
        return False
    
    logger.info("✅ جميع ملفات Cloudflare موجودة")
    return True

def check_hosting_files():
    """فحص ملفات الاستضافة"""
    hosting_files = [
        "Dockerfile",
        "docker-compose.yml",
        "Procfile",
        "render.yaml",
        "app.json",
        "runtime.txt",
        "start.sh",
        "start.bat",
        ".gitignore"
    ]
    
    missing_files = []
    for file_name in hosting_files:
        if not os.path.exists(file_name):
            missing_files.append(file_name)
        else:
            logger.info(f"✅ {file_name}")
    
    if missing_files:
        logger.warning(f"⚠️ ملفات استضافة مفقودة: {missing_files}")
        return True  # ليست ضرورية للتشغيل الأساسي
    
    logger.info("✅ جميع ملفات الاستضافة موجودة")
    return True

def check_file_sizes():
    """فحص أحجام الملفات"""
    large_files = []
    total_size = 0
    
    for root, dirs, files in os.walk("."):
        for file in files:
            file_path = os.path.join(root, file)
            try:
                size = os.path.getsize(file_path)
                total_size += size
                
                # فحص الملفات الكبيرة (أكبر من 10 MB)
                if size > 10 * 1024 * 1024:
                    large_files.append((file_path, size))
            except OSError:
                continue
    
    if large_files:
        logger.warning("⚠️ ملفات كبيرة الحجم:")
        for file_path, size in large_files:
            logger.warning(f"   {file_path}: {size / (1024*1024):.1f} MB")
    
    logger.info(f"📊 الحجم الإجمالي: {total_size / (1024*1024):.1f} MB")
    return True

def main():
    """الدالة الرئيسية"""
    logger.info("🔍 فحص حزمة البوت...")
    logger.info("=" * 50)
    
    checks = [
        ("الملفات الأساسية", check_essential_files),
        ("المجلدات", check_directories), 
        ("ملفات Cloudflare", check_cloudflare_files),
        ("ملفات الاستضافة", check_hosting_files),
        ("أحجام الملفات", check_file_sizes)
    ]
    
    all_passed = True
    
    for check_name, check_func in checks:
        logger.info(f"\n📋 فحص {check_name}:")
        if not check_func():
            all_passed = False
    
    logger.info("\n" + "=" * 50)
    
    if all_passed:
        logger.info("🎉 الحزمة جاهزة للنشر!")
        logger.info("📋 الخطوات التالية:")
        logger.info("   1. حدث ملف .env بالمعلومات الصحيحة")
        logger.info("   2. اختر منصة الاستضافة من HOSTING_GUIDES.md")
        logger.info("   3. انشر البوت")
        logger.info("   4. انشر صفحة التحميل على Cloudflare")
        return True
    else:
        logger.error("❌ الحزمة غير مكتملة!")
        logger.error("يرجى إصلاح المشاكل أعلاه قبل النشر")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
