# 🚀 دليل رفع البوت على Heroku

## الخطوة 1: إنشاء حساب
1. اذهب إلى [heroku.com](https://heroku.com)
2. اضغط "Sign up for free"
3. أكمل التسجيل وفعل الحساب

## الخطوة 2: إنشاء تطبيق جديد
1. من لوحة التحكم اضغط "New" ثم "Create new app"
2. اختر اسم للتطبيق (مثل: minecraft-mods-bot-2024)
3. اختر المنطقة (Europe أو United States)
4. اضغط "Create app"

## الخطوة 3: رفع الملفات (طريقة GitHub)
### الطريقة الأولى: GitHub
1. ارفع ملفاتك على GitHub أولاً
2. في Heroku اذهب إلى تبويب "Deploy"
3. اختر "GitHub" كـ Deployment method
4. ابحث عن repository وصله
5. ا<PERSON><PERSON><PERSON> "Deploy Branch"

### الطريقة الثانية: Heroku CLI (رفع مباشر)
1. حمل Heroku CLI من [هنا](https://devcenter.heroku.com/articles/heroku-cli)
2. افتح Command Prompt في مجلد البوت
3. نفذ الأوامر التالية:
```bash
heroku login
heroku git:remote -a your-app-name
git init
git add .
git commit -m "Initial commit"
git push heroku main
```

## الخطوة 4: إعداد متغيرات البيئة
1. اذهب إلى تبويب "Settings"
2. اضغط "Reveal Config Vars"
3. أضف المتغيرات:
```
BOT_TOKEN=your_bot_token_here
ADMIN_CHAT_ID=your_admin_id_here
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_key
ENVIRONMENT=production
DEBUG=false
```

## الخطوة 5: إعداد Dynos
1. اذهب إلى تبويب "Resources"
2. في قسم "Free Dynos" فعل "worker"
3. أوقف "web" إذا كان مفعلاً (لتوفير الساعات)

## الخطوة 6: مراقبة اللوجز
1. اذهب إلى "More" ثم "View logs"
2. أو استخدم الأمر: `heroku logs --tail -a your-app-name`

## نصائح مهمة:
- ⚠️ 550 ساعة مجانية فقط (~18 ساعة يومياً)
- ⚠️ ينام بعد 30 دقيقة من عدم النشاط
- ✅ يستيقظ فوراً عند أول طلب
- 💡 استخدم خدمة ping لمنع النوم
- 💡 فعل "worker" dyno بدلاً من "web" لتوفير الذاكرة
