#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح شامل لمشكلة "sending is disabled" في Render
"""

import os
import sys
import asyncio
import logging
import requests
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class RenderFixer:
    """فئة لإصلاح مشاكل Render"""
    
    def __init__(self):
        self.bot_token = os.getenv('TELEGRAM_BOT_TOKEN') or os.getenv('BOT_TOKEN')
        self.is_render = bool(os.getenv('RENDER'))
        
    def check_environment(self):
        """فحص البيئة والإعدادات"""
        logger.info("🔍 فحص بيئة Render...")
        
        if not self.is_render:
            logger.warning("⚠️ لا يبدو أن هذا Render")
            return False
            
        if not self.bot_token:
            logger.error("❌ رمز البوت مفقود")
            return False
            
        logger.info("✅ البيئة صحيحة")
        return True
    
    def clear_webhook(self):
        """تنظيف webhook"""
        logger.info("🧹 تنظيف webhook...")
        
        try:
            url = f"https://api.telegram.org/bot{self.bot_token}/deleteWebhook"
            params = {
                'drop_pending_updates': True
            }
            
            response = requests.post(url, params=params, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('ok'):
                    logger.info("✅ تم تنظيف webhook بنجاح")
                    return True
                else:
                    logger.error(f"❌ فشل في تنظيف webhook: {result.get('description')}")
                    return False
            else:
                logger.error(f"❌ خطأ HTTP في تنظيف webhook: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ خطأ في تنظيف webhook: {e}")
            return False
    
    def test_bot_connection(self):
        """اختبار اتصال البوت"""
        logger.info("🔗 اختبار اتصال البوت...")
        
        try:
            url = f"https://api.telegram.org/bot{self.bot_token}/getMe"
            response = requests.get(url, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('ok'):
                    bot_info = result.get('result', {})
                    logger.info(f"✅ البوت متصل: {bot_info.get('username', 'Unknown')}")
                    return True
                else:
                    logger.error(f"❌ فشل في الاتصال: {result.get('description')}")
                    return False
            else:
                logger.error(f"❌ خطأ HTTP: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ خطأ في الاتصال: {e}")
            return False
    
    def check_bot_permissions(self):
        """فحص صلاحيات البوت"""
        logger.info("🔐 فحص صلاحيات البوت...")
        
        try:
            # اختبار إرسال رسالة للمطور
            admin_id = os.getenv('ADMIN_CHAT_ID') or os.getenv('YOUR_CHAT_ID')
            if not admin_id:
                logger.warning("⚠️ معرف المطور غير محدد")
                return True
            
            url = f"https://api.telegram.org/bot{self.bot_token}/sendMessage"
            data = {
                'chat_id': admin_id,
                'text': '🔧 اختبار إصلاح Render - البوت يعمل بشكل طبيعي',
                'parse_mode': 'HTML'
            }
            
            response = requests.post(url, json=data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('ok'):
                    logger.info("✅ البوت يمكنه إرسال الرسائل")
                    return True
                else:
                    error_desc = result.get('description', 'Unknown error')
                    if 'bot was blocked' in error_desc.lower():
                        logger.warning("⚠️ البوت محظور من قبل المطور")
                    elif 'chat not found' in error_desc.lower():
                        logger.warning("⚠️ معرف المطور غير صحيح")
                    else:
                        logger.error(f"❌ خطأ في الإرسال: {error_desc}")
                    return False
            else:
                logger.error(f"❌ خطأ HTTP في الإرسال: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ خطأ في فحص الصلاحيات: {e}")
            return False
    
    def fix_render_issues(self):
        """إصلاح مشاكل Render الشائعة"""
        logger.info("🔧 إصلاح مشاكل Render...")
        
        fixes_applied = []
        
        # 1. تنظيف webhook
        if self.clear_webhook():
            fixes_applied.append("تنظيف webhook")
        
        # 2. تعيين متغيرات البيئة المطلوبة
        if not os.getenv('PYTHONUNBUFFERED'):
            os.environ['PYTHONUNBUFFERED'] = '1'
            fixes_applied.append("تعيين PYTHONUNBUFFERED")
        
        # 3. تعيين timeout مناسب
        if not os.getenv('TELEGRAM_TIMEOUT'):
            os.environ['TELEGRAM_TIMEOUT'] = '60'
            fixes_applied.append("تعيين TELEGRAM_TIMEOUT")
        
        # 4. تعيين إعدادات polling
        if not os.getenv('USE_POLLING'):
            os.environ['USE_POLLING'] = 'true'
            fixes_applied.append("تفعيل polling")
        
        logger.info(f"✅ تم تطبيق {len(fixes_applied)} إصلاحات: {', '.join(fixes_applied)}")
        return fixes_applied
    
    def create_render_config(self):
        """إنشاء ملف إعدادات Render"""
        logger.info("📝 إنشاء ملف إعدادات Render...")
        
        config_content = """# إعدادات Render المحسنة
import os

# إعدادات البوت
BOT_CONFIG = {
    'use_polling': True,
    'drop_pending_updates': True,
    'timeout': 60,
    'read_timeout': 60,
    'write_timeout': 60,
    'connect_timeout': 60,
    'pool_timeout': 60
}

# إعدادات الشبكة
NETWORK_CONFIG = {
    'max_retries': 5,
    'retry_delay': 3,
    'backoff_factor': 2
}

# تطبيق الإعدادات
def apply_render_config():
    \"\"\"تطبيق إعدادات Render\"\"\"
    os.environ['PYTHONUNBUFFERED'] = '1'
    os.environ['USE_POLLING'] = 'true'
    os.environ['TELEGRAM_TIMEOUT'] = '60'
    return BOT_CONFIG, NETWORK_CONFIG
"""
        
        try:
            with open('render_config.py', 'w', encoding='utf-8') as f:
                f.write(config_content)
            logger.info("✅ تم إنشاء ملف إعدادات Render")
            return True
        except Exception as e:
            logger.error(f"❌ فشل في إنشاء ملف الإعدادات: {e}")
            return False
    
    def run_full_diagnosis(self):
        """تشغيل تشخيص كامل"""
        logger.info("🔍 بدء التشخيص الكامل لـ Render...")
        
        results = {
            'environment_check': False,
            'webhook_cleared': False,
            'bot_connection': False,
            'bot_permissions': False,
            'fixes_applied': [],
            'config_created': False
        }
        
        # 1. فحص البيئة
        results['environment_check'] = self.check_environment()
        
        if results['environment_check']:
            # 2. تنظيف webhook
            results['webhook_cleared'] = self.clear_webhook()
            
            # 3. اختبار اتصال البوت
            results['bot_connection'] = self.test_bot_connection()
            
            # 4. فحص صلاحيات البوت
            results['bot_permissions'] = self.check_bot_permissions()
            
            # 5. تطبيق الإصلاحات
            results['fixes_applied'] = self.fix_render_issues()
            
            # 6. إنشاء ملف الإعدادات
            results['config_created'] = self.create_render_config()
        
        return results
    
    def print_diagnosis_report(self, results):
        """طباعة تقرير التشخيص"""
        logger.info("\n" + "="*60)
        logger.info("📊 تقرير تشخيص Render")
        logger.info("="*60)
        
        status_icon = lambda x: "✅" if x else "❌"
        
        logger.info(f"{status_icon(results['environment_check'])} فحص البيئة")
        logger.info(f"{status_icon(results['webhook_cleared'])} تنظيف webhook")
        logger.info(f"{status_icon(results['bot_connection'])} اتصال البوت")
        logger.info(f"{status_icon(results['bot_permissions'])} صلاحيات البوت")
        logger.info(f"{status_icon(bool(results['fixes_applied']))} تطبيق الإصلاحات")
        logger.info(f"{status_icon(results['config_created'])} إنشاء ملف الإعدادات")
        
        if results['fixes_applied']:
            logger.info(f"\n🔧 الإصلاحات المطبقة:")
            for fix in results['fixes_applied']:
                logger.info(f"   • {fix}")
        
        # تقييم عام
        total_checks = 6
        passed_checks = sum([
            results['environment_check'],
            results['webhook_cleared'],
            results['bot_connection'],
            results['bot_permissions'],
            bool(results['fixes_applied']),
            results['config_created']
        ])
        
        success_rate = (passed_checks / total_checks) * 100
        
        logger.info(f"\n📈 معدل النجاح: {success_rate:.1f}% ({passed_checks}/{total_checks})")
        
        if success_rate >= 80:
            logger.info("🎉 التشخيص ناجح! البوت جاهز للعمل في Render")
        elif success_rate >= 60:
            logger.info("⚠️ التشخيص جزئي. قد تحتاج لمراجعة بعض الإعدادات")
        else:
            logger.info("❌ التشخيص فاشل. يرجى مراجعة الأخطاء أعلاه")

def main():
    """الدالة الرئيسية"""
    print("🔧 أداة إصلاح مشاكل Render")
    print("="*50)
    
    fixer = RenderFixer()
    results = fixer.run_full_diagnosis()
    fixer.print_diagnosis_report(results)
    
    return 0 if results['environment_check'] and results['bot_connection'] else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
