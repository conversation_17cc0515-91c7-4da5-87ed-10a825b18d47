#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نسخة مصححة من دالة main
"""

async def main():
    """Run the bot with user-centric proposal, broadcast, and force-enable features."""
    global application_instance, shutdown_event

    try:
        # إعداد معالجات الإشارات للإغلاق الآمن
        setup_signal_handlers()
        logger.info("✅ تم إعداد معالجات الإشارات للإغلاق الآمن")

        # إصلاح مشاكل الاستضافة أولاً
        logger.info("🔧 إصلاح مشاكل الاستضافة...")
        try:
            setup_hosting_environment()
            logger.info("✅ تم إصلاح مشاكل الاستضافة")
        except Exception as e:
            logger.warning(f"⚠️ تحذير في إصلاح الاستضافة: {e}")

        # تطبيق إعدادات الشبكة المحسنة
        if NETWORK_CONFIG_ENABLED:
            try:
                apply_network_fixes()
                apply_network_optimizations()
                logger.info("✅ تم تطبيق إعدادات الشبكة المحسنة")
            except Exception as e:
                logger.warning(f"⚠️ فشل في تطبيق بعض إعدادات الشبكة: {e}")

        # تطبيق التحسينات الأمنية
        if ENHANCED_SECURITY_ENABLED:
            try:
                log_security_event("system_startup", "system", "Bot starting with enhanced security")
                logger.info("✅ تم تفعيل النظام الأمني المحسن")
            except Exception as e:
                logger.warning(f"⚠️ فشل في تطبيق بعض التحسينات الأمنية: {e}")

        logger.info("🔧 Initializing files...")
        initialize_files()

        # التحقق من الجداول المطلوبة (مرة واحدة فقط)
        logger.info("🔧 التحقق من الجداول المطلوبة...")
        try:
            # فحص سريع للجداول الأساسية
            from supabase_client import safe_supabase_request, SUPABASE_URL, SUPABASE_KEY

            # التحقق من وجود إعدادات Supabase أولاً
            if not SUPABASE_URL or not SUPABASE_KEY:
                logger.warning("⚠️ إعدادات Supabase مفقودة")
                logger.info("💡 تأكد من إعداد SUPABASE_URL و SUPABASE_KEY في ملف .env")
            else:
                # اختبار الوصول لجدول واحد فقط للتأكد من الاتصال
                test_url = f"{SUPABASE_URL}/rest/v1/mods?limit=1"
                test_response = safe_supabase_request('GET', test_url)

                if test_response and test_response.status_code == 200:
                    logger.info("✅ الاتصال مع قاعدة البيانات يعمل")
                    logger.info("✅ الجداول الأساسية متاحة")
                elif test_response and test_response.status_code == 401:
                    logger.warning("⚠️ مشكلة في مفتاح Supabase - تحقق من SUPABASE_KEY")
                elif test_response and test_response.status_code == 404:
                    logger.warning("⚠️ جدول mods غير موجود - تحقق من إنشاء الجداول")
                else:
                    logger.warning("⚠️ مشكلة في الاتصال مع قاعدة البيانات")
                    logger.info("💡 تأكد من إنشاء الجداول في Supabase Dashboard")

        except ImportError as e:
            logger.warning(f"خطأ في استيراد supabase_client: {e}")
            logger.info("💡 تحقق من وجود ملف supabase_client.py")
        except Exception as e:
            logger.warning(f"خطأ في التحقق من الجداول: {e}")
            logger.info("💡 تأكد من صحة إعدادات SUPABASE_URL و SUPABASE_KEY")

        # تخطي إنشاء جداول الإشعارات لتجنب التكرار
        logger.info("✅ تم تخطي فحص جداول الإشعارات لتجنب الرسائل المتكررة")

        # فحص الاتصال بالإنترنت أولاً مع تشخيص محسن
        logger.info("🌐 فحص الاتصال بالإنترنت...")

        # فحص DNS أولاً - محسن للاستضافة السحابية
        dns_working = False

        # في بيئة الاستضافة السحابية، نستخدم طرق أخرى للفحص
        if os.getenv('RENDER') or os.getenv('HEROKU') or os.getenv('RAILWAY'):
            logger.info("🌐 فحص DNS في بيئة استضافة سحابية...")
            try:
                import urllib.request
                # فحص بسيط للاتصال
                req = urllib.request.Request('https://www.google.com', headers={'User-Agent': 'Mozilla/5.0'})
                with urllib.request.urlopen(req, timeout=10) as response:
                    if response.status == 200:
                        logger.info("✅ DNS وحل أسماء النطاقات يعمل")
                        dns_working = True
            except Exception as e:
                logger.warning(f"⚠️ فحص DNS السحابي: {e}")
        else:
            # الطريقة التقليدية للبيئة المحلية
            dns_servers = ["8.8.8.8", "1.1.1.1", "208.67.222.222"]

            for dns in dns_servers:
                try:
                    import socket
                    socket.create_connection((dns, 53), timeout=10)
                    logger.info(f"✅ DNS {dns} متاح")
                    dns_working = True
                    break
                except Exception as e:
                    logger.warning(f"⚠️ DNS {dns} غير متاح: {e}")

        if not dns_working:
            logger.warning("⚠️ لم يتم التأكد من DNS، سيتم المحاولة مع إعدادات محسنة...")
            # لا نوقف البوت، بل نحاول المتابعة
        else:
            # فحص حل أسماء النطاقات
            try:
                import socket
                socket.gethostbyname("api.telegram.org")
                logger.info("✅ حل أسماء النطاقات يعمل")
            except Exception as e:
                logger.warning(f"⚠️ تحذير في حل أسماء النطاقات: {e}")
                # لا نوقف البوت، بل نحاول المتابعة

        # باقي الكود...
        logger.info("🚀 Starting bot...")

        # Validate essential config
        if not TOKEN or not YOUR_CHAT_ID:
            logger.critical("🔥 BOT_TOKEN or ADMIN_CHAT_ID not set! Exiting.")
            return
        
        try:
            admin_id_int = int(YOUR_CHAT_ID)
        except ValueError:
            logger.critical(f"🔥 ADMIN_CHAT_ID ('{YOUR_CHAT_ID}') is not a valid integer! Admin features might not work correctly. Exiting.")
            return

        # إنشاء التطبيق
        application = Application.builder().token(TOKEN).build()
        application_instance = application

        # Initialize the application
        logger.info("🔗 محاولة تهيئة الاتصال مع Telegram...")
        await application.initialize()
        logger.info("✅ تم تهيئة الاتصال مع Telegram بنجاح!")

        # إعداد المعالجات
        setup_handlers(application)

        # إعداد Job Queue
        job_queue = application.job_queue
        job_queue.run_repeating(check_users_and_propose_mods, interval=timedelta(minutes=1), first=timedelta(seconds=10), name="check_users_propose_send")
        job_queue.run_repeating(fetch_and_preview_batch, interval=timedelta(minutes=15), first=timedelta(seconds=30), name="check_new_files")
        job_queue.run_repeating(cleanup_stale_pending_items, interval=timedelta(hours=PENDING_CLEANUP_INTERVAL_HOURS), first=timedelta(minutes=5), name="cleanup_pending")
        
        # إضافة job لمراقبة صحة البوت
        job_queue.run_repeating(health_check_job, interval=timedelta(minutes=5), first=timedelta(seconds=60), name="health_monitor")
        
        # إضافة job لتنظيف الذاكرة
        job_queue.run_repeating(memory_cleanup_job, interval=timedelta(minutes=10), first=timedelta(minutes=2), name="memory_cleanup")

        logger.info("✅ Bot setup complete. Starting polling...")

        # Start the application
        await application.start()
        await application.updater.start_polling(drop_pending_updates=True, allowed_updates=Update.ALL_TYPES)

        # Keep the bot running indefinitely with health monitoring
        try:
            logger.info("🤖 البوت يعمل الآن... (اضغط Ctrl+C للإيقاف)")
            
            # Wait forever (until interrupted) with health monitoring
            while not shutdown_event.is_set():
                await asyncio.sleep(1)
                
                # تحديث نبضة الصحة
                health_monitor.heartbeat()
                
                # فحص صحة البوت كل دقيقة
                if int(time.time()) % 60 == 0:
                    if not health_monitor.check_health():
                        logger.warning("⚠️ البوت غير صحي، محاولة إعادة التعيين...")
                        health_monitor.reset_errors()
                
                # تنظيف الذاكرة دورياً
                if memory_manager.should_cleanup():
                    memory_manager.cleanup_memory()
                
                # فحص استخدام الذاكرة وتنظيف إجباري إذا لزم الأمر
                memory_manager.force_cleanup_if_needed()

        except KeyboardInterrupt:
            logger.info("📡 تم استلام إشارة المقاطعة، بدء الإغلاق الآمن...")
            shutdown_event.set()
        except Exception as e:
            logger.error(f"❌ خطأ غير متوقع في الحلقة الرئيسية: {e}")
            health_monitor.report_error()
        finally:
            logger.info("🔄 بدء عملية الإغلاق الآمن...")
            try:
                # إيقاف التطبيق بشكل آمن
                await application.stop()
                await application.shutdown()
                logger.info("✅ تم إيقاف التطبيق بنجاح")
                
                # تنظيف الذاكرة
                gc.collect()
                logger.info("🧹 تم تنظيف الذاكرة")
                
            except Exception as shutdown_error:
                logger.error(f"❌ خطأ أثناء الإغلاق: {shutdown_error}")

    except ValueError as ve:
        logger.critical(f"🔥 Configuration Error: {ve}")
    except Exception as e:
        logger.critical(f"🔥 Bot failed to start: {str(e)}", exc_info=True)
        health_monitor.report_error()
        raise

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
