import os
import requests
import logging
from dotenv import load_dotenv

# --- Basic Setup ---
# Load environment variables from .env file
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# --- Supabase Configuration ---
# Attempt to load from secure_config first, then fallback to environment variables
try:
    from secure_config import get_supabase_url, get_supabase_key
    SUPABASE_URL = get_supabase_url()
    SUPABASE_KEY = get_supabase_key()
    if not SUPABASE_URL or not SUPABASE_KEY:
        raise ValueError("Supabase URL or Key not found in secure_config")
    logger.info("✅ Successfully loaded Supabase configuration from secure_config.")
except (ImportError, ValueError):
    logger.warning("⚠️ Could not load from secure_config. Falling back to environment variables.")
    SUPABASE_URL = os.environ.get("SUPABASE_URL")
    SUPABASE_KEY = os.environ.get("SUPABASE_KEY")

# Validate configuration
if not SUPABASE_URL or not SUPABASE_KEY:
    logger.error("❌ CRITICAL: Supabase URL and Key are not configured. Please set them in your .env file or secure_config.py.")
    exit(1)

# Clean up URL and prepare headers
SUPABASE_URL = SUPABASE_URL.rstrip('/')
HEADERS = {
    'apikey': SUPABASE_KEY,
    'Authorization': f'Bearer {SUPABASE_KEY}',
    'Content-Type': 'application/json'
}

def execute_sql_from_file(file_path: str):
    """
    Reads an SQL file and executes its content against the Supabase database
    using the 'execute_sql' RPC endpoint.
    """
    logger.info(f"🚀 Starting schema update from file: {file_path}")

    # --- 1. Read the SQL file ---
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        if not sql_content.strip():
            logger.error(f"❌ SQL file '{file_path}' is empty. Aborting.")
            return
        logger.info(f"📄 Successfully read {len(sql_content)} characters from {file_path}.")
    except FileNotFoundError:
        logger.error(f"❌ SQL file not found at '{file_path}'. Aborting.")
        return
    except Exception as e:
        logger.error(f"❌ An error occurred while reading the file: {e}")
        return

    # --- 2. Prepare and execute the request ---
    rpc_url = f"{SUPABASE_URL}/rest/v1/rpc/execute_sql"
    payload = {"sql": sql_content} # The RPC function on Supabase is often just 'sql'

    logger.info(f"📡 Sending SQL to endpoint: {rpc_url}")

    try:
        # Note: Supabase RPC for executing raw SQL might not exist by default.
        # We are assuming an RPC function `execute_sql(sql TEXT)` has been created.
        # If not, this will fail, and the user needs to run the SQL manually.
        # A common alternative is to use the /pg-api/v1/raw endpoint if available.
        # Let's try the standard PostgREST endpoint for raw SQL execution first.
        
        # The correct endpoint for raw SQL is usually not an RPC one, but a direct query endpoint.
        # Let's use the standard Supabase SQL endpoint.
        sql_endpoint_url = f"{SUPABASE_URL}/rest/v1/sql" # This is a more standard endpoint
        
        # The direct SQL endpoint expects the query as the raw body, not JSON.
        sql_headers = HEADERS.copy()
        sql_headers['Content-Type'] = 'text/plain'

        # Let's try the RPC endpoint first as it's safer
        rpc_response = requests.post(rpc_url, headers=HEADERS, json=payload, timeout=60)

        if rpc_response.status_code == 200:
             logger.info("✅✅✅ Schema update completed successfully via RPC!")
             logger.info(f"Response: {rpc_response.text}")
             return

        logger.warning(f"⚠️ RPC endpoint failed with status {rpc_response.status_code}. Trying raw SQL endpoint...")
        
        # If RPC fails, try the raw SQL endpoint
        raw_sql_url = f"{SUPABASE_URL}/sql" # This is the direct SQL execution path
        raw_sql_headers = {
            'apikey': SUPABASE_KEY,
            'Authorization': f'Bearer {SUPABASE_KEY}',
            'Content-Type': 'application/sql'
        }
        
        response = requests.post(raw_sql_url, headers=raw_sql_headers, data=sql_content.encode('utf-8'), timeout=120)

        # --- 3. Process the response ---
        if response.status_code == 200:
            logger.info("✅✅✅ Schema update completed successfully!")
            logger.info("All tables and functions should now be up-to-date.")
        else:
            logger.error(f"❌❌❌ Schema update failed with status code: {response.status_code}")
            logger.error(f"Response Body: {response.text}")
            logger.error("Please review the error message from Supabase. You may need to apply the schema changes manually in the Supabase SQL Editor.")

    except requests.exceptions.RequestException as e:
        logger.error(f"❌ A network error occurred: {e}")
        logger.error("Please check your network connection and Supabase URL.")
    except Exception as e:
        logger.error(f"❌ An unexpected error occurred: {e}")

if __name__ == "__main__":
    # The SQL file to execute
    sql_file = "updated_schema.sql"
    execute_sql_from_file(sql_file)
