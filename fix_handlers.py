#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح شامل لمعالجات الأزرار والدوال المكررة
"""

import re
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def find_duplicate_handlers():
    """البحث عن معالجات مكررة"""
    with open('main.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن جميع معالجات CallbackQueryHandler
    pattern = r'application\.add_handler\(CallbackQueryHandler\([^,]+,\s*pattern="([^"]+)"\)'
    matches = re.findall(pattern, content)
    
    # تتبع الأنماط المكررة
    pattern_counts = {}
    for pattern in matches:
        pattern_counts[pattern] = pattern_counts.get(pattern, 0) + 1
    
    # عرض الأنماط المكررة
    duplicates = {k: v for k, v in pattern_counts.items() if v > 1}
    if duplicates:
        logger.warning("🔍 تم العثور على أنماط مكررة:")
        for pattern, count in duplicates.items():
            logger.warning(f"  - {pattern}: {count} مرات")
    else:
        logger.info("✅ لا توجد أنماط مكررة")
    
    return duplicates

def find_missing_functions():
    """البحث عن دوال مفقودة"""
    with open('main.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن جميع الدوال المستخدمة في المعالجات
    handler_pattern = r'CallbackQueryHandler\(([^,\)]+)'
    used_functions = re.findall(handler_pattern, content)
    
    # البحث عن جميع الدوال المعرفة
    function_pattern = r'async def ([a-zA-Z_][a-zA-Z0-9_]*)'
    defined_functions = re.findall(function_pattern, content)
    
    # البحث عن الدوال المفقودة
    missing_functions = []
    for func in used_functions:
        # تنظيف اسم الدالة
        func = func.strip()
        if func.startswith('lambda'):
            continue
        if func not in defined_functions:
            missing_functions.append(func)
    
    if missing_functions:
        logger.warning("🔍 دوال مفقودة:")
        for func in set(missing_functions):
            logger.warning(f"  - {func}")
    else:
        logger.info("✅ جميع الدوال موجودة")
    
    return missing_functions

def check_syntax_errors():
    """فحص الأخطاء النحوية"""
    try:
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # محاولة تجميع الكود
        compile(content, 'main.py', 'exec')
        logger.info("✅ لا توجد أخطاء نحوية")
        return True
        
    except SyntaxError as e:
        logger.error(f"❌ خطأ نحوي في السطر {e.lineno}: {e.msg}")
        logger.error(f"   النص: {e.text}")
        return False
    except Exception as e:
        logger.error(f"❌ خطأ في فحص الكود: {e}")
        return False

def fix_indentation_errors():
    """إصلاح أخطاء المسافات البادئة"""
    with open('main.py', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    fixed_lines = []
    in_try_block = False
    expected_indent = 0
    
    for i, line in enumerate(lines):
        line_num = i + 1
        stripped = line.strip()
        
        # تخطي الأسطر الفارغة والتعليقات
        if not stripped or stripped.startswith('#'):
            fixed_lines.append(line)
            continue
        
        # حساب المسافة البادئة الحالية
        current_indent = len(line) - len(line.lstrip())
        
        # التحقق من بداية try block
        if stripped.startswith('try:'):
            in_try_block = True
            expected_indent = current_indent + 4
        
        # التحقق من except/finally
        elif stripped.startswith(('except', 'finally')):
            if in_try_block:
                # التأكد من أن except/finally في نفس مستوى try
                try_indent = expected_indent - 4
                if current_indent != try_indent:
                    logger.info(f"🔧 إصلاح مسافة بادئة في السطر {line_num}")
                    line = ' ' * try_indent + stripped + '\n'
        
        # التحقق من نهاية try block
        elif in_try_block and current_indent <= expected_indent - 4:
            in_try_block = False
        
        fixed_lines.append(line)
    
    # كتابة الملف المصحح
    with open('main.py', 'w', encoding='utf-8') as f:
        f.writelines(fixed_lines)
    
    logger.info("✅ تم إصلاح أخطاء المسافات البادئة")

def remove_duplicate_handlers():
    """إزالة المعالجات المكررة"""
    with open('main.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن المعالجات المكررة وإزالتها
    lines = content.split('\n')
    seen_patterns = set()
    cleaned_lines = []
    
    for line in lines:
        # التحقق من وجود معالج
        match = re.search(r'pattern="([^"]+)"', line)
        if match:
            pattern = match.group(1)
            if pattern in seen_patterns:
                logger.info(f"🗑️ إزالة معالج مكرر: {pattern}")
                continue
            seen_patterns.add(pattern)
        
        cleaned_lines.append(line)
    
    # كتابة الملف المنظف
    with open('main.py', 'w', encoding='utf-8') as f:
        f.write('\n'.join(cleaned_lines))
    
    logger.info("✅ تم إزالة المعالجات المكررة")

def optimize_handlers():
    """تحسين ترتيب المعالجات"""
    with open('main.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن قسم المعالجات
    start_marker = "# --- Language & Main Menu ---"
    end_marker = "# --- Setup Additional Handlers ---"
    
    start_idx = content.find(start_marker)
    end_idx = content.find(end_marker)
    
    if start_idx == -1 or end_idx == -1:
        logger.warning("⚠️ لم يتم العثور على قسم المعالجات")
        return
    
    # استخراج المعالجات
    handlers_section = content[start_idx:end_idx]
    
    # ترتيب المعالجات حسب الأولوية
    priority_patterns = [
        "^lang_",
        "^main_menu$",
        "^admin_",
        "^setting_",
        "^user_",
        "^channel_",
        "^monetization_",
        "^ads_"
    ]
    
    # تجميع المعالجات حسب الأولوية
    organized_handlers = []
    remaining_handlers = handlers_section.split('\n')
    
    for priority in priority_patterns:
        matching_lines = []
        non_matching_lines = []
        
        for line in remaining_handlers:
            if re.search(f'pattern="[^"]*{priority}', line):
                matching_lines.append(line)
            else:
                non_matching_lines.append(line)
        
        organized_handlers.extend(matching_lines)
        remaining_handlers = non_matching_lines
    
    # إضافة المعالجات المتبقية
    organized_handlers.extend(remaining_handlers)
    
    # إعادة بناء المحتوى
    new_content = (
        content[:start_idx] + 
        '\n'.join(organized_handlers) + 
        content[end_idx:]
    )
    
    with open('main.py', 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    logger.info("✅ تم تحسين ترتيب المعالجات")

def main():
    """الدالة الرئيسية"""
    logger.info("🔧 بدء إصلاح معالجات البوت...")
    logger.info("=" * 50)
    
    # 1. فحص الأخطاء النحوية
    logger.info("1️⃣ فحص الأخطاء النحوية...")
    if not check_syntax_errors():
        logger.info("🔧 إصلاح أخطاء المسافات البادئة...")
        fix_indentation_errors()
        
        # فحص مرة أخرى
        if not check_syntax_errors():
            logger.error("❌ لا يزال هناك أخطاء نحوية")
            return False
    
    # 2. البحث عن المعالجات المكررة
    logger.info("2️⃣ البحث عن المعالجات المكررة...")
    duplicates = find_duplicate_handlers()
    if duplicates:
        logger.info("🔧 إزالة المعالجات المكررة...")
        remove_duplicate_handlers()
    
    # 3. البحث عن الدوال المفقودة
    logger.info("3️⃣ البحث عن الدوال المفقودة...")
    missing = find_missing_functions()
    if missing:
        logger.warning("⚠️ يوجد دوال مفقودة، يرجى إضافتها يدوياً")
    
    # 4. تحسين ترتيب المعالجات
    logger.info("4️⃣ تحسين ترتيب المعالجات...")
    optimize_handlers()
    
    # 5. فحص نهائي
    logger.info("5️⃣ فحص نهائي...")
    if check_syntax_errors():
        logger.info("🎉 تم إصلاح جميع المشاكل بنجاح!")
        return True
    else:
        logger.error("❌ لا تزال هناك مشاكل")
        return False

if __name__ == "__main__":
    main()
