-- SQL لإنشاء الجداول المفقودة
-- تاريخ الإنشاء: 2025-08-04 20:32:09.490933


                CREATE TABLE IF NOT EXISTS public.saved_notifications (
                    id SERIAL PRIMARY KEY,
                    title TEXT NOT NULL,
                    message TEXT NOT NULL,
                    message_type TEXT DEFAULT 'text' CHECK (message_type IN ('text', 'photo', 'video', 'document')),
                    media_url TEXT,
                    button_text TEXT,
                    button_url TEXT,
                    created_by TEXT NOT NULL,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    is_active BOOLEAN DEFAULT true
                );
                CREATE INDEX IF NOT EXISTS idx_saved_notifications_created_by ON public.saved_notifications(created_by);
                CREATE INDEX IF NOT EXISTS idx_saved_notifications_active ON public.saved_notifications(is_active);
            