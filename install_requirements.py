#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة تثبيت المتطلبات التلقائية
Automatic Requirements Installation Tool

هذا الملف يقوم بتثبيت جميع المكتبات المطلوبة للبوت تلقائياً
This file automatically installs all required libraries for the bot
"""

import subprocess
import sys
import os
import logging
from typing import List, Tuple

# إعداد التسجيل
logging.basicConfig(
    format="%(asctime)s - %(levelname)s - %(message)s",
    level=logging.INFO,
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger(__name__)

def check_python_version() -> bool:
    """التحقق من إصدار Python"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        logger.error(f"❌ إصدار Python غير مدعوم: {version.major}.{version.minor}")
        logger.error("❌ يتطلب Python 3.8 أو أحدث")
        return False
    
    logger.info(f"✅ إصدار Python مدعوم: {version.major}.{version.minor}.{version.micro}")
    return True

def upgrade_pip() -> bool:
    """ترقية pip لأحدث إصدار"""
    try:
        logger.info("🔄 ترقية pip...")
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "--upgrade", "pip"
        ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        logger.info("✅ تم ترقية pip بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        logger.warning(f"⚠️ فشل في ترقية pip: {e}")
        return False

def install_package(package: str, retries: int = 3) -> bool:
    """تثبيت مكتبة واحدة مع إعادة المحاولة"""
    for attempt in range(retries):
        try:
            logger.info(f"📦 تثبيت {package}... (المحاولة {attempt + 1}/{retries})")
            
            # محاولة التثبيت مع خيارات مختلفة
            install_commands = [
                [sys.executable, "-m", "pip", "install", package],
                [sys.executable, "-m", "pip", "install", "--user", package],
                [sys.executable, "-m", "pip", "install", "--no-cache-dir", package],
            ]
            
            for cmd in install_commands:
                try:
                    subprocess.check_call(cmd, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                    logger.info(f"✅ تم تثبيت {package} بنجاح")
                    return True
                except subprocess.CalledProcessError:
                    continue
            
            # إذا فشلت جميع الطرق، جرب مع --force-reinstall
            try:
                subprocess.check_call([
                    sys.executable, "-m", "pip", "install", "--force-reinstall", package
                ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                logger.info(f"✅ تم تثبيت {package} بنجاح (إعادة تثبيت)")
                return True
            except subprocess.CalledProcessError:
                pass
                
        except Exception as e:
            logger.warning(f"⚠️ فشل في تثبيت {package} (المحاولة {attempt + 1}): {e}")
            
        if attempt < retries - 1:
            logger.info(f"🔄 إعادة المحاولة لتثبيت {package}...")
    
    logger.error(f"❌ فشل في تثبيت {package} بعد {retries} محاولات")
    return False

def read_requirements() -> List[str]:
    """قراءة ملف requirements.txt"""
    requirements_file = "requirements.txt"
    
    if not os.path.exists(requirements_file):
        logger.error(f"❌ ملف {requirements_file} غير موجود")
        return []
    
    try:
        with open(requirements_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # تنظيف الأسطر وإزالة التعليقات والأسطر الفارغة
        packages = []
        for line in lines:
            line = line.strip()
            if line and not line.startswith('#') and not line.startswith('-'):
                packages.append(line)
        
        logger.info(f"📋 تم العثور على {len(packages)} مكتبة في {requirements_file}")
        return packages
        
    except Exception as e:
        logger.error(f"❌ فشل في قراءة {requirements_file}: {e}")
        return []

def install_requirements() -> Tuple[int, int]:
    """تثبيت جميع المتطلبات"""
    packages = read_requirements()
    
    if not packages:
        logger.error("❌ لا توجد مكتبات للتثبيت")
        return 0, 0
    
    logger.info("🚀 بدء تثبيت المتطلبات...")
    logger.info("=" * 50)
    
    successful = 0
    failed = 0
    
    for package in packages:
        if install_package(package):
            successful += 1
        else:
            failed += 1
    
    logger.info("=" * 50)
    logger.info(f"📊 نتائج التثبيت:")
    logger.info(f"   ✅ نجح: {successful}")
    logger.info(f"   ❌ فشل: {failed}")
    logger.info(f"   📦 المجموع: {len(packages)}")
    
    return successful, failed

def verify_installation() -> bool:
    """التحقق من نجاح التثبيت"""
    logger.info("🔍 التحقق من التثبيت...")
    
    critical_packages = [
        'telegram',
        'dotenv', 
        'requests',
        'flask',
        'supabase'
    ]
    
    failed_imports = []
    
    for package in critical_packages:
        try:
            __import__(package)
            logger.info(f"✅ {package}: متوفر")
        except ImportError:
            logger.error(f"❌ {package}: غير متوفر")
            failed_imports.append(package)
    
    if failed_imports:
        logger.error(f"❌ فشل في استيراد المكتبات الأساسية: {failed_imports}")
        return False
    
    logger.info("✅ جميع المكتبات الأساسية متوفرة")
    return True

def main():
    """الدالة الرئيسية"""
    print("🤖 أداة تثبيت متطلبات البوت")
    print("=" * 50)
    
    # التحقق من إصدار Python
    if not check_python_version():
        sys.exit(1)
    
    # ترقية pip
    upgrade_pip()
    
    # تثبيت المتطلبات
    successful, failed = install_requirements()
    
    # التحقق من التثبيت
    if verify_installation():
        print("\n🎉 تم تثبيت جميع المتطلبات بنجاح!")
        print("✅ البوت جاهز للتشغيل")
        
        if failed > 0:
            print(f"⚠️ تحذير: فشل في تثبيت {failed} مكتبة، لكن المكتبات الأساسية متوفرة")
        
        return 0
    else:
        print("\n❌ فشل في تثبيت بعض المكتبات الأساسية")
        print("🔧 يرجى تشغيل الأداة مرة أخرى أو تثبيت المكتبات يدوياً")
        return 1

if __name__ == "__main__":
    sys.exit(main())
