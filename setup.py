#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت إعداد البوت للاستضافة
Bot Hosting Setup Script
"""

import os
import sys
import subprocess
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def install_requirements():
    """تثبيت المتطلبات"""
    logger.info("📦 تثبيت المتطلبات...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        logger.info("✅ تم تثبيت المتطلبات بنجاح")
        return True
    except Exception as e:
        logger.error(f"❌ فشل في تثبيت المتطلبات: {e}")
        return False

def setup_env_file():
    """إعداد ملف البيئة"""
    logger.info("⚙️ إعداد ملف البيئة...")
    
    if os.path.exists(".env"):
        logger.info("✅ ملف .env موجود بالفعل")
        return True
    
    if os.path.exists(".env.example"):
        shutil.copy(".env.example", ".env")
        logger.info("✅ تم إنشاء ملف .env من .env.example")
        logger.info("📝 يرجى تحديث ملف .env بالمعلومات الصحيحة")
        return True
    else:
        logger.error("❌ ملف .env.example غير موجود")
        return False

def main():
    """الدالة الرئيسية"""
    logger.info("🚀 بدء إعداد البوت للاستضافة...")
    
    # تثبيت المتطلبات
    if not install_requirements():
        return False
    
    # إعداد ملف البيئة
    if not setup_env_file():
        return False
    
    logger.info("🎉 تم إعداد البوت بنجاح!")
    logger.info("📋 الخطوات التالية:")
    logger.info("   1. حدث ملف .env بالمعلومات الصحيحة")
    logger.info("   2. شغل البوت: python main.py")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
