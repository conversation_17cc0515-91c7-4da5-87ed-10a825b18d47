# ===================================================================
# اختبار قاعدة بيانات المستخدمين - User Database Test
# ===================================================================
# هذا الملف يحتوي على اختبارات شاملة لقاعدة بيانات المستخدمين الجديدة
# للتأكد من عمل جميع الوظائف بشكل صحيح
# ===================================================================

import os
import sys
import logging
from datetime import datetime, timezone
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# استيراد دوال قاعدة البيانات
try:
    from user_database_client import *
except ImportError as e:
    logger.error(f"فشل في استيراد user_database_client: {e}")
    sys.exit(1)

# ===================================================================
# بيانات الاختبار
# ===================================================================

TEST_USER_ID = "test_user_123456"
TEST_CHANNEL_ID = "-1001234567890"
TEST_MOD_ID = 999999

# ===================================================================
# دوال الاختبار
# ===================================================================

def test_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    logger.info("🔍 اختبار الاتصال بقاعدة البيانات...")
    
    try:
        result = test_user_database_connection()
        if result:
            logger.info("✅ نجح الاتصال بقاعدة البيانات")
            return True
        else:
            logger.error("❌ فشل الاتصال بقاعدة البيانات")
            return False
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار الاتصال: {e}")
        return False

def test_user_operations():
    """اختبار عمليات المستخدمين"""
    logger.info("🔍 اختبار عمليات المستخدمين...")
    
    try:
        # إنشاء مستخدم
        logger.info("📝 إنشاء مستخدم اختبار...")
        result = create_or_update_user(TEST_USER_ID, "test_user", "Test User", "ar")
        if not result:
            logger.error("❌ فشل في إنشاء المستخدم")
            return False
        
        # جلب بيانات المستخدم
        logger.info("📖 جلب بيانات المستخدم...")
        user = get_user(TEST_USER_ID)
        if not user:
            logger.error("❌ فشل في جلب بيانات المستخدم")
            return False
        
        logger.info(f"✅ تم جلب المستخدم: {user['username']}")
        
        # تحديث نشاط المستخدم
        logger.info("🔄 تحديث نشاط المستخدم...")
        result = update_user_activity(TEST_USER_ID)
        if not result:
            logger.error("❌ فشل في تحديث نشاط المستخدم")
            return False
        
        logger.info("✅ نجحت عمليات المستخدمين")
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار عمليات المستخدمين: {e}")
        return False

def test_channel_operations():
    """اختبار عمليات القنوات"""
    logger.info("🔍 اختبار عمليات القنوات...")
    
    try:
        # إضافة قناة
        logger.info("📝 إضافة قناة اختبار...")
        result = add_user_channel(
            TEST_USER_ID, 
            TEST_CHANNEL_ID, 
            "Test Channel",
            is_default=True,
            active=True,
            publish_interval=60,
            preview_enabled=False,
            message_format="classic",
            channel_lang="ar",
            mod_categories=["addons", "shaders"],
            mc_versions=["1.21+", "1.20+"]
        )
        if not result:
            logger.error("❌ فشل في إضافة القناة")
            return False
        
        # جلب قنوات المستخدم
        logger.info("📖 جلب قنوات المستخدم...")
        channels = get_user_channels(TEST_USER_ID)
        if not channels:
            logger.error("❌ فشل في جلب قنوات المستخدم")
            return False
        
        logger.info(f"✅ تم جلب {len(channels)} قناة")
        
        # جلب القناة الافتراضية
        logger.info("📖 جلب القناة الافتراضية...")
        default_channel = get_user_default_channel(TEST_USER_ID)
        if default_channel != TEST_CHANNEL_ID:
            logger.error("❌ القناة الافتراضية غير صحيحة")
            return False
        
        # تحديث إعدادات القناة
        logger.info("🔄 تحديث إعدادات القناة...")
        result = update_channel_settings(TEST_USER_ID, TEST_CHANNEL_ID, publish_interval=120)
        if not result:
            logger.error("❌ فشل في تحديث إعدادات القناة")
            return False
        
        logger.info("✅ نجحت عمليات القنوات")
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار عمليات القنوات: {e}")
        return False

def test_subscription_operations():
    """اختبار عمليات الاشتراكات"""
    logger.info("🔍 اختبار عمليات الاشتراكات...")
    
    try:
        # إنشاء اشتراك
        logger.info("📝 إنشاء اشتراك اختبار...")
        result = create_user_subscription(TEST_USER_ID, "premium", None, is_active=True)
        if not result:
            logger.error("❌ فشل في إنشاء الاشتراك")
            return False
        
        # جلب اشتراك المستخدم
        logger.info("📖 جلب اشتراك المستخدم...")
        subscription = get_user_subscription(TEST_USER_ID)
        if not subscription:
            logger.error("❌ فشل في جلب اشتراك المستخدم")
            return False
        
        logger.info(f"✅ تم جلب الاشتراك: {subscription['subscription_type']}")
        
        logger.info("✅ نجحت عمليات الاشتراكات")
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار عمليات الاشتراكات: {e}")
        return False

def test_feature_operations():
    """اختبار عمليات المميزات"""
    logger.info("🔍 اختبار عمليات المميزات...")
    
    try:
        # تفعيل ميزة
        logger.info("📝 تفعيل ميزة اختبار...")
        result = set_user_feature(TEST_USER_ID, "unlimited_channels", True, None, "test")
        if not result:
            logger.error("❌ فشل في تفعيل الميزة")
            return False
        
        # التحقق من تفعيل الميزة
        logger.info("📖 التحقق من تفعيل الميزة...")
        is_enabled = is_user_feature_enabled(TEST_USER_ID, "unlimited_channels")
        if not is_enabled:
            logger.error("❌ الميزة غير مفعلة")
            return False
        
        # جلب جميع مميزات المستخدم
        logger.info("📖 جلب جميع مميزات المستخدم...")
        features = get_user_features(TEST_USER_ID)
        if not features:
            logger.error("❌ فشل في جلب مميزات المستخدم")
            return False
        
        logger.info(f"✅ تم جلب {len(features)} ميزة")
        
        logger.info("✅ نجحت عمليات المميزات")
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار عمليات المميزات: {e}")
        return False

def test_feedback_operations():
    """اختبار عمليات التقييمات"""
    logger.info("🔍 اختبار عمليات التقييمات...")
    
    try:
        # حفظ تقييم
        logger.info("📝 حفظ تقييم اختبار...")
        result = save_user_feedback(TEST_USER_ID, TEST_MOD_ID, "like", 5, "Great mod!")
        if not result:
            logger.error("❌ فشل في حفظ التقييم")
            return False
        
        # جلب التقييم
        logger.info("📖 جلب التقييم...")
        feedback = get_user_feedback(TEST_USER_ID, TEST_MOD_ID)
        if not feedback:
            logger.error("❌ فشل في جلب التقييم")
            return False
        
        logger.info(f"✅ تم جلب التقييم: {feedback['feedback_type']}")
        
        logger.info("✅ نجحت عمليات التقييمات")
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار عمليات التقييمات: {e}")
        return False

def test_mod_status_operations():
    """اختبار عمليات حالة المودات"""
    logger.info("🔍 اختبار عمليات حالة المودات...")
    
    try:
        # حفظ حالة مود
        logger.info("📝 حفظ حالة مود اختبار...")
        result = save_user_mod_status(TEST_USER_ID, TEST_MOD_ID, "published", TEST_CHANNEL_ID)
        if not result:
            logger.error("❌ فشل في حفظ حالة المود")
            return False
        
        # التحقق من نشر المود
        logger.info("📖 التحقق من نشر المود...")
        is_published = is_mod_published_for_user(TEST_USER_ID, TEST_MOD_ID)
        if not is_published:
            logger.error("❌ المود غير منشور")
            return False
        
        # جلب المودات المنشورة
        logger.info("📖 جلب المودات المنشورة...")
        published_mods = get_user_published_mods(TEST_USER_ID)
        if TEST_MOD_ID not in published_mods:
            logger.error("❌ المود غير موجود في قائمة المنشورة")
            return False
        
        logger.info(f"✅ تم جلب {len(published_mods)} مود منشور")
        
        logger.info("✅ نجحت عمليات حالة المودات")
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار عمليات حالة المودات: {e}")
        return False

def test_admin_settings_operations():
    """اختبار عمليات إعدادات المسؤول"""
    logger.info("🔍 اختبار عمليات إعدادات المسؤول...")
    
    try:
        # تعيين إعداد
        logger.info("📝 تعيين إعداد مسؤول اختبار...")
        result = set_admin_setting("test_setting", {"enabled": True, "value": 100}, "Test setting")
        if not result:
            logger.error("❌ فشل في تعيين إعداد المسؤول")
            return False
        
        # جلب الإعداد
        logger.info("📖 جلب إعداد المسؤول...")
        setting = get_admin_setting("test_setting")
        if not setting:
            logger.error("❌ فشل في جلب إعداد المسؤول")
            return False
        
        logger.info(f"✅ تم جلب الإعداد: {setting}")
        
        # جلب جميع الإعدادات
        logger.info("📖 جلب جميع إعدادات المسؤول...")
        all_settings = get_all_admin_settings()
        if not all_settings:
            logger.error("❌ فشل في جلب جميع إعدادات المسؤول")
            return False
        
        logger.info(f"✅ تم جلب {len(all_settings)} إعداد")
        
        logger.info("✅ نجحت عمليات إعدادات المسؤول")
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار عمليات إعدادات المسؤول: {e}")
        return False

def cleanup_test_data():
    """تنظيف بيانات الاختبار"""
    logger.info("🧹 تنظيف بيانات الاختبار...")
    
    try:
        # حذف القناة
        delete_user_channel(TEST_USER_ID, TEST_CHANNEL_ID)
        
        # يمكن إضافة المزيد من عمليات التنظيف هنا
        
        logger.info("✅ تم تنظيف بيانات الاختبار")
        
    except Exception as e:
        logger.warning(f"⚠️ تحذير في تنظيف بيانات الاختبار: {e}")

# ===================================================================
# الدالة الرئيسية للاختبار
# ===================================================================

def run_all_tests():
    """تشغيل جميع الاختبارات"""
    logger.info("🚀 بدء اختبار قاعدة بيانات المستخدمين...")
    logger.info("=" * 60)
    
    # قائمة الاختبارات
    tests = [
        ("الاتصال", test_connection),
        ("عمليات المستخدمين", test_user_operations),
        ("عمليات القنوات", test_channel_operations),
        ("عمليات الاشتراكات", test_subscription_operations),
        ("عمليات المميزات", test_feature_operations),
        ("عمليات التقييمات", test_feedback_operations),
        ("عمليات حالة المودات", test_mod_status_operations),
        ("عمليات إعدادات المسؤول", test_admin_settings_operations)
    ]
    
    # تشغيل كل اختبار
    results = {}
    for name, test_func in tests:
        logger.info(f"\n📋 اختبار {name}...")
        try:
            results[name] = test_func()
            if results[name]:
                logger.info(f"✅ نجح اختبار {name}")
            else:
                logger.error(f"❌ فشل اختبار {name}")
        except Exception as e:
            logger.error(f"❌ خطأ في اختبار {name}: {e}")
            results[name] = False
    
    # تنظيف بيانات الاختبار
    cleanup_test_data()
    
    # عرض النتائج النهائية
    logger.info("\n" + "=" * 60)
    logger.info("📊 نتائج الاختبارات:")
    logger.info("=" * 60)
    
    passed = 0
    failed = 0
    
    for name, success in results.items():
        status = "✅ نجح" if success else "❌ فشل"
        logger.info(f"   {name}: {status}")
        if success:
            passed += 1
        else:
            failed += 1
    
    logger.info("=" * 60)
    logger.info(f"📈 الإجمالي: {passed} نجح، {failed} فشل")
    
    if failed == 0:
        logger.info("🎉 جميع الاختبارات نجحت! قاعدة البيانات تعمل بشكل صحيح")
    else:
        logger.warning("⚠️ بعض الاختبارات فشلت. يرجى مراجعة السجلات أعلاه")
    
    return failed == 0

# ===================================================================
# تشغيل الاختبارات إذا تم استدعاء الملف مباشرة
# ===================================================================

if __name__ == "__main__":
    print("🧪 اختبار قاعدة بيانات المستخدمين")
    print("=" * 40)
    
    # التأكد من وجود إعدادات قاعدة البيانات
    if not USER_DB_URL or not USER_DB_KEY:
        print("❌ خطأ: إعدادات قاعدة بيانات المستخدمين مفقودة")
        print("يرجى إعداد المتغيرات التالية في ملف .env:")
        print("USER_SUPABASE_URL=your_user_database_url")
        print("USER_SUPABASE_KEY=your_user_database_key")
        sys.exit(1)
    
    # تشغيل الاختبارات
    success = run_all_tests()
    
    if success:
        print("\n✅ جميع الاختبارات نجحت!")
        sys.exit(0)
    else:
        print("\n❌ بعض الاختبارات فشلت!")
        sys.exit(1)
