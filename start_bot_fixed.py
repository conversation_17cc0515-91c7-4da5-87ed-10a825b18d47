#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل البوت المحسن
يتعامل مع جميع المشاكل المحتملة ويضمن تشغيل البوت بنجاح
"""

import os
import sys
import time
import logging
import requests
import subprocess
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

def check_python_processes():
    """فحص العمليات الجارية للبوت"""
    try:
        if os.name == 'nt':  # Windows
            result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq python.exe'], 
                                  capture_output=True, text=True)
            processes = result.stdout
            
            # البحث عن عمليات البوت
            bot_processes = []
            for line in processes.split('\n'):
                if 'python.exe' in line and ('main.py' in line or 'bot' in line.lower()):
                    bot_processes.append(line.strip())
            
            return bot_processes
        else:  # Linux/Mac
            result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
            processes = result.stdout
            
            bot_processes = []
            for line in processes.split('\n'):
                if 'python' in line and ('main.py' in line or 'bot' in line.lower()):
                    bot_processes.append(line.strip())
            
            return bot_processes
    except Exception as e:
        logger.warning(f"فشل في فحص العمليات: {e}")
        return []

def kill_existing_bot_processes():
    """إيقاف العمليات الموجودة للبوت"""
    try:
        logger.info("🔍 فحص العمليات الجارية للبوت...")

        if os.name == 'nt':  # Windows
            try:
                # فحص العمليات أولاً
                result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq python.exe'],
                                      capture_output=True, text=True)

                if 'python.exe' in result.stdout:
                    logger.info("🔄 إيقاف عمليات Python الموجودة...")
                    # إيقاف عمليات Python بحذر
                    subprocess.run(['taskkill', '/F', '/IM', 'python.exe'],
                                 capture_output=True, check=False)
                    logger.info("✅ تم إيقاف عمليات Python الموجودة")
                else:
                    logger.info("✅ لا توجد عمليات Python جارية")
            except Exception as e:
                logger.warning(f"تحذير في إيقاف عمليات Windows: {e}")
        else:  # Linux/Mac
            try:
                subprocess.run(['pkill', '-f', 'main.py'], capture_output=True, check=False)
                subprocess.run(['pkill', '-f', 'bot'], capture_output=True, check=False)
                logger.info("✅ تم إيقاف عمليات البوت الموجودة")
            except Exception as e:
                logger.warning(f"تحذير في إيقاف عمليات Linux/Mac: {e}")

        # انتظار لضمان إيقاف العمليات
        time.sleep(3)
        return True

    except Exception as e:
        logger.warning(f"تحذير في إيقاف العمليات: {e}")
        return True  # متابعة حتى لو فشل

def clear_telegram_conflicts():
    """مسح تضارب Telegram"""
    try:
        bot_token = os.getenv('BOT_TOKEN')
        if not bot_token:
            logger.error("❌ لم يتم العثور على BOT_TOKEN")
            return False
        
        logger.info("🔧 مسح تضارب Telegram...")
        
        # مسح webhook
        webhook_url = f"https://api.telegram.org/bot{bot_token}/deleteWebhook"
        response = requests.post(webhook_url, json={"drop_pending_updates": True}, timeout=10)
        
        if response.status_code == 200:
            logger.info("✅ تم مسح webhook")
        
        # مسح التحديثات المعلقة
        updates_url = f"https://api.telegram.org/bot{bot_token}/getUpdates"
        
        for i in range(3):
            response = requests.get(updates_url, params={"timeout": 1}, timeout=10)
            if response.status_code == 200:
                result = response.json()
                if result.get('ok') and result.get('result'):
                    updates = result['result']
                    if updates:
                        last_id = updates[-1]['update_id']
                        # مسح التحديثات
                        requests.get(updates_url, params={"offset": last_id + 1}, timeout=10)
                        logger.info(f"✅ تم مسح {len(updates)} تحديث معلق")
                    else:
                        break
        
        logger.info("✅ تم مسح جميع تضارب Telegram")
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في مسح تضارب Telegram: {e}")
        return False

def check_requirements():
    """فحص المتطلبات الأساسية"""
    logger.info("🔍 فحص المتطلبات الأساسية...")
    
    # فحص متغيرات البيئة
    required_vars = ['BOT_TOKEN', 'ADMIN_CHAT_ID', 'SUPABASE_URL', 'SUPABASE_KEY']
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        logger.error(f"❌ متغيرات البيئة المفقودة: {', '.join(missing_vars)}")
        return False
    
    # فحص الملفات المطلوبة
    required_files = ['main.py', 'supabase_client.py', 'web_server.py', 'telegram_web_app.py']
    missing_files = []
    
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        logger.error(f"❌ الملفات المفقودة: {', '.join(missing_files)}")
        return False
    
    logger.info("✅ جميع المتطلبات متوفرة")
    return True

def start_bot():
    """تشغيل البوت"""
    try:
        logger.info("🚀 بدء تشغيل البوت...")
        
        # تشغيل البوت
        process = subprocess.Popen([sys.executable, 'main.py'], 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.STDOUT,
                                 universal_newlines=True,
                                 bufsize=1)
        
        logger.info("✅ تم تشغيل البوت بنجاح!")
        logger.info("📝 سجلات البوت:")
        logger.info("=" * 50)
        
        # عرض السجلات
        try:
            for line in process.stdout:
                print(line.rstrip())
                
                # فحص إذا كان هناك خطأ في التضارب
                if "Conflict: terminated by other getUpdates request" in line:
                    logger.warning("⚠️ تم اكتشاف تضارب Telegram، محاولة الإصلاح...")
                    process.terminate()
                    time.sleep(5)
                    clear_telegram_conflicts()
                    time.sleep(3)
                    return start_bot()  # إعادة التشغيل
                    
        except KeyboardInterrupt:
            logger.info("🛑 تم إيقاف البوت بواسطة المستخدم")
            process.terminate()
            
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل البوت: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    logger.info("🤖 مرحباً بك في نظام تشغيل البوت المحسن")
    logger.info("=" * 50)
    
    # 1. فحص المتطلبات
    if not check_requirements():
        logger.error("❌ فشل في فحص المتطلبات")
        return False
    
    # 2. إيقاف العمليات الموجودة
    kill_existing_bot_processes()
    
    # 3. مسح تضارب Telegram
    clear_telegram_conflicts()
    
    # 4. انتظار قصير
    logger.info("⏳ انتظار 5 ثوانِ قبل التشغيل...")
    time.sleep(5)
    
    # 5. تشغيل البوت
    start_bot()

if __name__ == "__main__":
    main()
