# ===================================================================
# أداة هجرة بيانات المستخدمين من الملفات المحلية إلى قاعدة البيانات
# User Data Migration Tool from Local Files to Database
# ===================================================================
# هذا الملف يحتوي على دوال لنقل جميع البيانات المخزنة محلياً في ملفات JSON
# إلى قاعدة بيانات Supabase الجديدة
# ===================================================================

import os
import json
import logging
from datetime import datetime, timezone
from typing import Dict, List, Any
from user_database_client import *

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# ===================================================================
# دوال مساعدة لقراءة الملفات المحلية
# ===================================================================

def load_json_file_safe(filename: str, default_value: Any = None) -> Any:
    """قراءة ملف JSON بشكل آمن"""
    if not os.path.exists(filename):
        logger.warning(f"الملف غير موجود: {filename}")
        return default_value or {}
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            data = json.load(f)
            logger.info(f"تم تحميل الملف: {filename}")
            return data
    except (json.JSONDecodeError, IOError) as e:
        logger.error(f"خطأ في قراءة الملف {filename}: {e}")
        return default_value or {}

def backup_local_files():
    """إنشاء نسخة احتياطية من الملفات المحلية"""
    try:
        import shutil
        from datetime import datetime
        
        backup_dir = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        os.makedirs(backup_dir, exist_ok=True)
        
        files_to_backup = [
            'user_channels.json',
            'all_users.json',
            'user_subscriptions.json',
            'user_feature_activation.json',
            'user_feedback.json',
            'user_mods_status.json',
            'user_blocked_mods.json',
            'user_invitations.json',
            'admin_settings.json',
            'pending_publication.json'
        ]
        
        for filename in files_to_backup:
            if os.path.exists(filename):
                shutil.copy2(filename, backup_dir)
                logger.info(f"تم نسخ {filename} إلى {backup_dir}")
        
        logger.info(f"✅ تم إنشاء نسخة احتياطية في: {backup_dir}")
        return backup_dir
        
    except Exception as e:
        logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
        return None

# ===================================================================
# دوال هجرة البيانات
# ===================================================================

def migrate_users_data():
    """هجرة بيانات المستخدمين من all_users.json"""
    logger.info("🔄 بدء هجرة بيانات المستخدمين...")
    
    try:
        all_users = load_json_file_safe('all_users.json', {})
        
        if not all_users:
            logger.warning("لا توجد بيانات مستخدمين للهجرة")
            return True
        
        success_count = 0
        error_count = 0
        
        for user_id, user_data in all_users.items():
            try:
                # استخراج البيانات
                username = user_data.get('username', '')
                full_name = user_data.get('full_name', '')
                lang = user_data.get('lang', 'ar')
                
                # إنشاء/تحديث المستخدم
                if create_or_update_user(user_id, username, full_name, lang):
                    success_count += 1
                    logger.info(f"✅ تم نقل المستخدم: {user_id}")
                else:
                    error_count += 1
                    logger.error(f"❌ فشل في نقل المستخدم: {user_id}")
                    
            except Exception as e:
                error_count += 1
                logger.error(f"❌ خطأ في نقل المستخدم {user_id}: {e}")
        
        logger.info(f"📊 نتائج هجرة المستخدمين: نجح {success_count}, فشل {error_count}")
        return error_count == 0
        
    except Exception as e:
        logger.error(f"❌ خطأ عام في هجرة المستخدمين: {e}")
        return False

def migrate_channels_data():
    """هجرة بيانات القنوات من user_channels.json"""
    logger.info("🔄 بدء هجرة بيانات القنوات...")
    
    try:
        user_channels = load_json_file_safe('user_channels.json', {})
        
        if not user_channels:
            logger.warning("لا توجد بيانات قنوات للهجرة")
            return True
        
        success_count = 0
        error_count = 0
        
        for user_id, user_data in user_channels.items():
            try:
                # التحقق من وجود قنوات للمستخدم
                channels = user_data.get('channels', {})
                if not channels:
                    continue
                
                # معالجة كل قناة
                for channel_id, channel_data in channels.items():
                    try:
                        # استخراج البيانات
                        channel_settings = {
                            'active': channel_data.get('active', True),
                            'publish_interval': channel_data.get('publish_interval', 60),
                            'preview_enabled': channel_data.get('preview_enabled', False),
                            'message_format': channel_data.get('message_format', 'classic'),
                            'channel_lang': channel_data.get('channel_lang', 'ar'),
                            'mod_categories': channel_data.get('mod_categories', []),
                            'mc_versions': channel_data.get('mc_versions', [])
                        }
                        
                        # تحديد القناة الافتراضية
                        default_channel = user_data.get('user_settings', {}).get('default_channel')
                        is_default = (channel_id == default_channel)
                        
                        # إضافة القناة
                        if add_user_channel(user_id, channel_id, is_default=is_default, **channel_settings):
                            success_count += 1
                            logger.info(f"✅ تم نقل القناة: {channel_id} للمستخدم {user_id}")
                        else:
                            error_count += 1
                            logger.error(f"❌ فشل في نقل القناة: {channel_id} للمستخدم {user_id}")
                            
                    except Exception as e:
                        error_count += 1
                        logger.error(f"❌ خطأ في نقل القناة {channel_id} للمستخدم {user_id}: {e}")
                        
            except Exception as e:
                error_count += 1
                logger.error(f"❌ خطأ في معالجة قنوات المستخدم {user_id}: {e}")
        
        logger.info(f"📊 نتائج هجرة القنوات: نجح {success_count}, فشل {error_count}")
        return error_count == 0
        
    except Exception as e:
        logger.error(f"❌ خطأ عام في هجرة القنوات: {e}")
        return False

def migrate_subscriptions_data():
    """هجرة بيانات الاشتراكات من user_subscriptions.json"""
    logger.info("🔄 بدء هجرة بيانات الاشتراكات...")
    
    try:
        subscriptions = load_json_file_safe('user_subscriptions.json', {})
        
        if not subscriptions:
            logger.warning("لا توجد بيانات اشتراكات للهجرة")
            return True
        
        success_count = 0
        error_count = 0
        
        for user_id, subscription_data in subscriptions.items():
            try:
                # استخراج البيانات
                subscription_type = subscription_data.get('type', 'free')
                end_date = subscription_data.get('end_date')
                is_active = subscription_data.get('active', True)
                auto_renew = subscription_data.get('auto_renew', False)
                payment_method = subscription_data.get('payment_method')
                
                # إنشاء الاشتراك
                if create_user_subscription(user_id, subscription_type, end_date, 
                                          is_active=is_active, auto_renew=auto_renew, 
                                          payment_method=payment_method):
                    success_count += 1
                    logger.info(f"✅ تم نقل اشتراك المستخدم: {user_id}")
                else:
                    error_count += 1
                    logger.error(f"❌ فشل في نقل اشتراك المستخدم: {user_id}")
                    
            except Exception as e:
                error_count += 1
                logger.error(f"❌ خطأ في نقل اشتراك المستخدم {user_id}: {e}")
        
        logger.info(f"📊 نتائج هجرة الاشتراكات: نجح {success_count}, فشل {error_count}")
        return error_count == 0
        
    except Exception as e:
        logger.error(f"❌ خطأ عام في هجرة الاشتراكات: {e}")
        return False

def migrate_feature_activation_data():
    """هجرة بيانات تفعيل المميزات من user_feature_activation.json"""
    logger.info("🔄 بدء هجرة بيانات تفعيل المميزات...")
    
    try:
        features = load_json_file_safe('user_feature_activation.json', {})
        
        if not features:
            logger.warning("لا توجد بيانات تفعيل مميزات للهجرة")
            return True
        
        success_count = 0
        error_count = 0
        
        for user_id, user_features in features.items():
            try:
                if not isinstance(user_features, dict):
                    continue
                
                # معالجة كل ميزة
                for feature_name, feature_data in user_features.items():
                    try:
                        # استخراج البيانات
                        if isinstance(feature_data, bool):
                            # تنسيق قديم - فقط true/false
                            is_enabled = feature_data
                            expiry_date = None
                            activation_source = "legacy"
                        elif isinstance(feature_data, dict):
                            # تنسيق جديد - كائن مع تفاصيل
                            is_enabled = feature_data.get('enabled', False)
                            expiry_date = feature_data.get('expiry_date')
                            activation_source = feature_data.get('source', 'legacy')
                        else:
                            continue
                        
                        # تعيين الميزة
                        if set_user_feature(user_id, feature_name, is_enabled, 
                                          expiry_date, activation_source):
                            success_count += 1
                            logger.info(f"✅ تم نقل ميزة {feature_name} للمستخدم {user_id}")
                        else:
                            error_count += 1
                            logger.error(f"❌ فشل في نقل ميزة {feature_name} للمستخدم {user_id}")
                            
                    except Exception as e:
                        error_count += 1
                        logger.error(f"❌ خطأ في نقل ميزة {feature_name} للمستخدم {user_id}: {e}")
                        
            except Exception as e:
                error_count += 1
                logger.error(f"❌ خطأ في معالجة مميزات المستخدم {user_id}: {e}")
        
        logger.info(f"📊 نتائج هجرة المميزات: نجح {success_count}, فشل {error_count}")
        return error_count == 0
        
    except Exception as e:
        logger.error(f"❌ خطأ عام في هجرة المميزات: {e}")
        return False

def migrate_feedback_data():
    """هجرة بيانات التقييمات من user_feedback.json"""
    logger.info("🔄 بدء هجرة بيانات التقييمات...")
    
    try:
        feedback = load_json_file_safe('user_feedback.json', {})
        
        if not feedback:
            logger.warning("لا توجد بيانات تقييمات للهجرة")
            return True
        
        success_count = 0
        error_count = 0
        
        for user_id, user_feedback in feedback.items():
            try:
                if not isinstance(user_feedback, dict):
                    continue
                
                # معالجة كل تقييم
                for mod_id_str, feedback_data in user_feedback.items():
                    try:
                        mod_id = int(mod_id_str)
                        
                        # استخراج البيانات
                        if isinstance(feedback_data, bool):
                            # تنسيق قديم - فقط true/false
                            feedback_type = "like" if feedback_data else "dislike"
                            rating = None
                            comment = None
                        elif isinstance(feedback_data, dict):
                            # تنسيق جديد - كائن مع تفاصيل
                            feedback_type = feedback_data.get('type', 'like')
                            rating = feedback_data.get('rating')
                            comment = feedback_data.get('comment')
                        else:
                            continue
                        
                        # حفظ التقييم
                        if save_user_feedback(user_id, mod_id, feedback_type, rating, comment):
                            success_count += 1
                            logger.info(f"✅ تم نقل تقييم المود {mod_id} للمستخدم {user_id}")
                        else:
                            error_count += 1
                            logger.error(f"❌ فشل في نقل تقييم المود {mod_id} للمستخدم {user_id}")
                            
                    except (ValueError, Exception) as e:
                        error_count += 1
                        logger.error(f"❌ خطأ في نقل تقييم المود {mod_id_str} للمستخدم {user_id}: {e}")
                        
            except Exception as e:
                error_count += 1
                logger.error(f"❌ خطأ في معالجة تقييمات المستخدم {user_id}: {e}")
        
        logger.info(f"📊 نتائج هجرة التقييمات: نجح {success_count}, فشل {error_count}")
        return error_count == 0
        
    except Exception as e:
        logger.error(f"❌ خطأ عام في هجرة التقييمات: {e}")
        return False

def migrate_mods_status_data():
    """هجرة بيانات حالة المودات من user_mods_status.json"""
    logger.info("🔄 بدء هجرة بيانات حالة المودات...")
    
    try:
        mods_status = load_json_file_safe('user_mods_status.json', {})
        
        if not mods_status:
            logger.warning("لا توجد بيانات حالة مودات للهجرة")
            return True
        
        success_count = 0
        error_count = 0
        
        for user_id, mod_list in mods_status.items():
            try:
                if not isinstance(mod_list, list):
                    continue
                
                # معالجة كل مود
                for mod_id in mod_list:
                    try:
                        # حفظ حالة المود
                        if save_user_mod_status(user_id, mod_id, "published"):
                            success_count += 1
                            logger.info(f"✅ تم نقل حالة المود {mod_id} للمستخدم {user_id}")
                        else:
                            error_count += 1
                            logger.error(f"❌ فشل في نقل حالة المود {mod_id} للمستخدم {user_id}")
                            
                    except Exception as e:
                        error_count += 1
                        logger.error(f"❌ خطأ في نقل حالة المود {mod_id} للمستخدم {user_id}: {e}")
                        
            except Exception as e:
                error_count += 1
                logger.error(f"❌ خطأ في معالجة حالة مودات المستخدم {user_id}: {e}")
        
        logger.info(f"📊 نتائج هجرة حالة المودات: نجح {success_count}, فشل {error_count}")
        return error_count == 0

    except Exception as e:
        logger.error(f"❌ خطأ عام في هجرة حالة المودات: {e}")
        return False

def migrate_blocked_mods_data():
    """هجرة بيانات المودات المحظورة من user_blocked_mods.json"""
    logger.info("🔄 بدء هجرة بيانات المودات المحظورة...")

    try:
        blocked_mods = load_json_file_safe('user_blocked_mods.json', {})

        if not blocked_mods:
            logger.warning("لا توجد بيانات مودات محظورة للهجرة")
            return True

        success_count = 0
        error_count = 0

        for user_id, mod_list in blocked_mods.items():
            try:
                if not isinstance(mod_list, list):
                    continue

                # معالجة كل مود محظور
                for mod_id in mod_list:
                    try:
                        # حظر المود
                        if block_mod_for_user(user_id, mod_id, "migrated from local data"):
                            success_count += 1
                            logger.info(f"✅ تم نقل حظر المود {mod_id} للمستخدم {user_id}")
                        else:
                            error_count += 1
                            logger.error(f"❌ فشل في نقل حظر المود {mod_id} للمستخدم {user_id}")

                    except Exception as e:
                        error_count += 1
                        logger.error(f"❌ خطأ في نقل حظر المود {mod_id} للمستخدم {user_id}: {e}")

            except Exception as e:
                error_count += 1
                logger.error(f"❌ خطأ في معالجة المودات المحظورة للمستخدم {user_id}: {e}")

        logger.info(f"📊 نتائج هجرة المودات المحظورة: نجح {success_count}, فشل {error_count}")
        return error_count == 0

    except Exception as e:
        logger.error(f"❌ خطأ عام في هجرة المودات المحظورة: {e}")
        return False

def migrate_invitations_data():
    """هجرة بيانات الدعوات من user_invitations.json"""
    logger.info("🔄 بدء هجرة بيانات الدعوات...")

    try:
        invitations = load_json_file_safe('user_invitations.json', {})

        if not invitations:
            logger.warning("لا توجد بيانات دعوات للهجرة")
            return True

        success_count = 0
        error_count = 0

        for user_id, user_invitations in invitations.items():
            try:
                if not isinstance(user_invitations, dict):
                    continue

                # معالجة كل دعوة
                for invitation_code, invitation_data in user_invitations.items():
                    try:
                        # استخراج البيانات
                        if isinstance(invitation_data, dict):
                            status = invitation_data.get('status', 'pending')
                            invited_user_id = invitation_data.get('invited_user_id')
                            reward_claimed = invitation_data.get('reward_claimed', False)
                            expires_days = invitation_data.get('expires_days', 30)
                        else:
                            # تنسيق قديم
                            status = 'pending'
                            invited_user_id = None
                            reward_claimed = False
                            expires_days = 30

                        # إنشاء الدعوة
                        created_code = create_invitation(user_id, invitation_code, expires_days)
                        if created_code:
                            # تحديث حالة الدعوة إذا كانت مقبولة
                            if status == 'accepted' and invited_user_id:
                                accept_invitation(invitation_code, invited_user_id)

                            success_count += 1
                            logger.info(f"✅ تم نقل الدعوة {invitation_code} للمستخدم {user_id}")
                        else:
                            error_count += 1
                            logger.error(f"❌ فشل في نقل الدعوة {invitation_code} للمستخدم {user_id}")

                    except Exception as e:
                        error_count += 1
                        logger.error(f"❌ خطأ في نقل الدعوة {invitation_code} للمستخدم {user_id}: {e}")

            except Exception as e:
                error_count += 1
                logger.error(f"❌ خطأ في معالجة دعوات المستخدم {user_id}: {e}")

        logger.info(f"📊 نتائج هجرة الدعوات: نجح {success_count}, فشل {error_count}")
        return error_count == 0

    except Exception as e:
        logger.error(f"❌ خطأ عام في هجرة الدعوات: {e}")
        return False

def migrate_admin_settings_data():
    """هجرة إعدادات المسؤول من admin_settings.json"""
    logger.info("🔄 بدء هجرة إعدادات المسؤول...")

    try:
        admin_settings = load_json_file_safe('admin_settings.json', {})

        if not admin_settings:
            logger.warning("لا توجد إعدادات مسؤول للهجرة")
            return True

        success_count = 0
        error_count = 0

        for setting_key, setting_value in admin_settings.items():
            try:
                # تعيين الإعداد
                if set_admin_setting(setting_key, setting_value, f"Migrated from local admin_settings.json"):
                    success_count += 1
                    logger.info(f"✅ تم نقل إعداد المسؤول: {setting_key}")
                else:
                    error_count += 1
                    logger.error(f"❌ فشل في نقل إعداد المسؤول: {setting_key}")

            except Exception as e:
                error_count += 1
                logger.error(f"❌ خطأ في نقل إعداد المسؤول {setting_key}: {e}")

        logger.info(f"📊 نتائج هجرة إعدادات المسؤول: نجح {success_count}, فشل {error_count}")
        return error_count == 0

    except Exception as e:
        logger.error(f"❌ خطأ عام في هجرة إعدادات المسؤول: {e}")
        return False

# ===================================================================
# الدالة الرئيسية للهجرة
# ===================================================================

def run_full_migration():
    """تشغيل هجرة شاملة لجميع البيانات"""
    logger.info("🚀 بدء الهجرة الشاملة للبيانات...")
    logger.info("=" * 60)

    # اختبار الاتصال أولاً
    if not test_user_database_connection():
        logger.error("❌ فشل في الاتصال بقاعدة بيانات المستخدمين")
        logger.error("يرجى التأكد من إعداد USER_SUPABASE_URL و USER_SUPABASE_KEY")
        return False

    # إنشاء نسخة احتياطية
    backup_dir = backup_local_files()
    if backup_dir:
        logger.info(f"✅ تم إنشاء نسخة احتياطية: {backup_dir}")
    else:
        logger.warning("⚠️ لم يتم إنشاء نسخة احتياطية")

    # قائمة دوال الهجرة
    migration_functions = [
        ("المستخدمين", migrate_users_data),
        ("القنوات", migrate_channels_data),
        ("الاشتراكات", migrate_subscriptions_data),
        ("تفعيل المميزات", migrate_feature_activation_data),
        ("التقييمات", migrate_feedback_data),
        ("حالة المودات", migrate_mods_status_data),
        ("المودات المحظورة", migrate_blocked_mods_data),
        ("الدعوات", migrate_invitations_data),
        ("إعدادات المسؤول", migrate_admin_settings_data)
    ]

    # تشغيل كل دالة هجرة
    results = {}
    for name, func in migration_functions:
        logger.info(f"\n📋 هجرة {name}...")
        try:
            results[name] = func()
            if results[name]:
                logger.info(f"✅ نجحت هجرة {name}")
            else:
                logger.error(f"❌ فشلت هجرة {name}")
        except Exception as e:
            logger.error(f"❌ خطأ في هجرة {name}: {e}")
            results[name] = False

    # عرض النتائج النهائية
    logger.info("\n" + "=" * 60)
    logger.info("📊 نتائج الهجرة النهائية:")
    logger.info("=" * 60)

    successful = 0
    failed = 0

    for name, success in results.items():
        status = "✅ نجح" if success else "❌ فشل"
        logger.info(f"   {name}: {status}")
        if success:
            successful += 1
        else:
            failed += 1

    logger.info("=" * 60)
    logger.info(f"📈 الإجمالي: {successful} نجح، {failed} فشل")

    if failed == 0:
        logger.info("🎉 تمت الهجرة بنجاح! يمكنك الآن استخدام قاعدة البيانات الجديدة")
        logger.info("💡 تذكر تحديث متغيرات البيئة في ملف .env")
    else:
        logger.warning("⚠️ تمت الهجرة مع بعض الأخطاء. يرجى مراجعة السجلات أعلاه")

    return failed == 0

# ===================================================================
# تشغيل الهجرة إذا تم استدعاء الملف مباشرة
# ===================================================================

if __name__ == "__main__":
    print("🔄 أداة هجرة بيانات المستخدمين")
    print("=" * 40)

    # التأكد من وجود إعدادات قاعدة البيانات
    if not USER_DB_URL or not USER_DB_KEY:
        print("❌ خطأ: إعدادات قاعدة بيانات المستخدمين مفقودة")
        print("يرجى إعداد المتغيرات التالية في ملف .env:")
        print("USER_SUPABASE_URL=your_user_database_url")
        print("USER_SUPABASE_KEY=your_user_database_key")
        exit(1)

    # تأكيد من المستخدم
    response = input("\n⚠️ هذا سينقل جميع البيانات من الملفات المحلية إلى قاعدة البيانات الجديدة.\nهل تريد المتابعة؟ (y/N): ")

    if response.lower() in ['y', 'yes', 'نعم']:
        success = run_full_migration()
        if success:
            print("\n✅ تمت الهجرة بنجاح!")
        else:
            print("\n❌ فشلت الهجرة. يرجى مراجعة السجلات.")
    else:
        print("تم إلغاء الهجرة.")
