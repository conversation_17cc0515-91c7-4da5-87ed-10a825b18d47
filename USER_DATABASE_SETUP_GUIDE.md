# 🗄️ دليل إعداد قاعدة بيانات المستخدمين - User Database Setup Guide

هذا الدليل يوضح كيفية إعداد قاعدة بيانات Supabase جديدة مخصصة لبيانات وإعدادات المستخدمين، منفصلة عن قاعدة بيانات المودات الحالية.

## 📋 المتطلبات الأساسية

### 1. إنشاء مشروع Supabase جديد
1. اذهب إلى [Supabase Dashboard](https://app.supabase.com)
2. انقر على "New Project"
3. اختر اسماً للمشروع مثل "bot-users-database"
4. اختر كلمة مرور قوية لقاعدة البيانات
5. اختر المنطقة الأقرب لك
6. انقر على "Create new project"

### 2. الحصول على بيانات الاتصال
بعد إنشاء المشروع، ستحتاج إلى:

1. **Project URL**: من Settings > API
   - مثال: `https://abcdefghijklmnop.supabase.co`

2. **API Key (anon/public)**: من Settings > API
   - مثال: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`

## 🔧 إعداد متغيرات البيئة

### إضافة المتغيرات الجديدة لملف .env

أضف المتغيرات التالية إلى ملف `.env` الخاص بك:

```env
# قاعدة بيانات المستخدمين الجديدة (منفصلة عن قاعدة بيانات المودات)
USER_SUPABASE_URL=https://your-user-project-url.supabase.co
USER_SUPABASE_KEY=your-user-database-anon-key

# قاعدة بيانات المودات الحالية (تبقى كما هي)
SUPABASE_URL=https://ytqxxodyecdeosnqoure.supabase.co
SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4

# باقي المتغيرات (تبقى كما هي)
BOT_TOKEN=7605181405:AAEBua03X_QdD6GkCqKWR1P02EtXIbnSkx4
ADMIN_CHAT_ID=7513880877
```

### للاستضافة على Render أو Heroku

أضف المتغيرات التالية في لوحة تحكم الاستضافة:

| المتغير | القيمة |
|---------|--------|
| `USER_SUPABASE_URL` | رابط مشروع قاعدة بيانات المستخدمين |
| `USER_SUPABASE_KEY` | مفتاح API لقاعدة بيانات المستخدمين |

## 🗃️ إنشاء الجداول في قاعدة البيانات

### الطريقة الأولى: استخدام SQL Editor في Supabase

1. اذهب إلى مشروع قاعدة بيانات المستخدمين في Supabase
2. انقر على "SQL Editor" في الشريط الجانبي
3. انسخ محتوى ملف `user_database_schema.sql` والصقه في المحرر
4. انقر على "Run" لتنفيذ الكود

### الطريقة الثانية: استخدام أداة سطر الأوامر

```bash
# تأكد من تثبيت Supabase CLI
npm install -g supabase

# تسجيل الدخول
supabase login

# ربط المشروع
supabase link --project-ref your-project-ref

# تنفيذ ملف SQL
supabase db push
```

## 📊 الجداول التي سيتم إنشاؤها

### 1. الجداول الأساسية:
- ✅ `users` - بيانات المستخدمين الأساسية
- ✅ `user_channels` - قنوات المستخدمين
- ✅ `channel_mod_categories` - فئات المودات للقنوات
- ✅ `channel_mc_versions` - إصدارات ماين كرافت للقنوات

### 2. جداول الاشتراكات والمميزات:
- ✅ `user_subscriptions` - اشتراكات المستخدمين
- ✅ `user_feature_activation` - تفعيل المميزات

### 3. جداول التفاعل:
- ✅ `user_feedback` - تقييمات المستخدمين
- ✅ `user_mods_status` - حالة المودات للمستخدمين
- ✅ `user_blocked_mods` - المودات المحظورة

### 4. جداول النظام:
- ✅ `user_invitations` - نظام الدعوات
- ✅ `admin_settings` - إعدادات المسؤول
- ✅ `pending_publications` - قائمة انتظار النشر

## 🔄 هجرة البيانات الموجودة

بعد إنشاء الجداول، يمكنك نقل البيانات الموجودة من الملفات المحلية:

### 1. تشغيل أداة الهجرة

```bash
python migrate_user_data.py
```

### 2. التحقق من نجاح الهجرة

ستظهر رسائل تأكيد لكل نوع من البيانات:
- ✅ المستخدمين
- ✅ القنوات
- ✅ الاشتراكات
- ✅ تفعيل المميزات
- ✅ التقييمات
- ✅ حالة المودات
- ✅ المودات المحظورة
- ✅ الدعوات
- ✅ إعدادات المسؤول

### 3. نسخة احتياطية تلقائية

ستقوم أداة الهجرة بإنشاء نسخة احتياطية من الملفات المحلية في مجلد:
```
backup_YYYYMMDD_HHMMSS/
```

## 🧪 اختبار النظام الجديد

### 1. اختبار الاتصال

```python
from user_database_client import test_user_database_connection

if test_user_database_connection():
    print("✅ الاتصال ناجح!")
else:
    print("❌ فشل الاتصال")
```

### 2. اختبار العمليات الأساسية

```python
from user_database_client import *

# إنشاء مستخدم
create_or_update_user("123456", "testuser", "Test User", "ar")

# إضافة قناة
add_user_channel("123456", "-1001234567890", "Test Channel", True)

# التحقق من البيانات
user = get_user("123456")
channels = get_user_channels("123456")

print(f"المستخدم: {user}")
print(f"القنوات: {channels}")
```

## 🔍 التحقق من البيانات في Supabase

### 1. استخدام Table Editor

1. اذهب إلى "Table Editor" في Supabase Dashboard
2. اختر الجدول المطلوب
3. تحقق من وجود البيانات

### 2. استخدام SQL Editor

```sql
-- فحص عدد المستخدمين
SELECT COUNT(*) as total_users FROM public.users;

-- فحص عدد القنوات
SELECT COUNT(*) as total_channels FROM public.user_channels;

-- فحص الإعدادات
SELECT * FROM public.admin_settings;

-- عرض إحصائيات شاملة
SELECT 
    (SELECT COUNT(*) FROM public.users) as users,
    (SELECT COUNT(*) FROM public.user_channels) as channels,
    (SELECT COUNT(*) FROM public.user_subscriptions) as subscriptions,
    (SELECT COUNT(*) FROM public.user_feature_activation) as features,
    (SELECT COUNT(*) FROM public.user_feedback) as feedback,
    (SELECT COUNT(*) FROM public.user_mods_status) as mods_status,
    (SELECT COUNT(*) FROM public.user_blocked_mods) as blocked_mods,
    (SELECT COUNT(*) FROM public.user_invitations) as invitations,
    (SELECT COUNT(*) FROM public.pending_publications) as pending;
```

## 🚀 تشغيل البوت مع قاعدة البيانات الجديدة

بعد إكمال الإعداد والهجرة:

1. تأكد من إضافة متغيرات البيئة الجديدة
2. أعد تشغيل البوت
3. راقب السجلات للتأكد من عدم وجود أخطاء
4. اختبر الوظائف الأساسية (إضافة قناة، تغيير الإعدادات، إلخ)

## ⚠️ ملاحظات مهمة

### 1. الأمان
- لا تشارك مفاتيح API مع أي شخص
- استخدم متغيرات البيئة دائماً
- فعّل Row Level Security (RLS) في Supabase إذا لزم الأمر

### 2. النسخ الاحتياطي
- احتفظ بنسخة احتياطية من الملفات المحلية
- فعّل النسخ الاحتياطي التلقائي في Supabase
- اختبر استعادة البيانات بشكل دوري

### 3. المراقبة
- راقب استخدام قاعدة البيانات في Supabase Dashboard
- تحقق من السجلات بانتظام
- فعّل التنبيهات للأخطاء

## 🆘 استكشاف الأخطاء وإصلاحها

### خطأ في الاتصال
```
❌ فشل الاتصال بقاعدة بيانات المستخدمين
```
**الحل:**
- تحقق من صحة `USER_SUPABASE_URL` و `USER_SUPABASE_KEY`
- تأكد من أن المشروع نشط في Supabase
- تحقق من الاتصال بالإنترنت

### خطأ في الجداول
```
❌ table "users" does not exist
```
**الحل:**
- تأكد من تنفيذ ملف `user_database_schema.sql`
- تحقق من إنشاء الجداول في Table Editor

### خطأ في الصلاحيات
```
❌ permission denied for table users
```
**الحل:**
- تحقق من صلاحيات API Key
- تأكد من استخدام المفتاح الصحيح (anon/public)

## 📞 الدعم

إذا واجهت أي مشاكل:
1. راجع السجلات في ملفات log
2. تحقق من إعدادات Supabase
3. تأكد من صحة متغيرات البيئة
4. اختبر الاتصال باستخدام `test_user_database_connection()`

---

✅ **تم إعداد قاعدة بيانات المستخدمين بنجاح!**

الآن يمكن للبوت العمل مع قاعدة بيانات منفصلة لبيانات المستخدمين، مما يضمن عدم فقدان البيانات عند رفع التحديثات على الاستضافة السحابية.
