#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح سريع للمشاكل الشائعة في البوت
Quick fix for common bot issues
"""

import os
import sys
import logging
import subprocess
import json

# إعداد التسجيل
logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    level=logging.INFO,
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger(__name__)

def install_missing_packages():
    """تثبيت الحزم المفقودة"""
    logger.info("🔧 فحص وتثبيت الحزم المفقودة...")
    
    required_packages = [
        'python-dotenv',
        'python-telegram-bot',
        'requests',
        'flask',
        'apscheduler'
    ]
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            logger.info(f"✅ {package}: مثبت")
        except ImportError:
            logger.info(f"📦 تثبيت {package}...")
            try:
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
                logger.info(f"✅ تم تثبيت {package}")
            except subprocess.CalledProcessError as e:
                logger.error(f"❌ فشل في تثبيت {package}: {e}")
                return False
    
    return True

def create_env_file():
    """إنشاء ملف .env إذا لم يكن موجوداً"""
    logger.info("🔧 فحص ملف .env...")
    
    if not os.path.exists('.env'):
        if os.path.exists('.env.example'):
            logger.info("📋 إنشاء ملف .env من .env.example...")
            try:
                with open('.env.example', 'r', encoding='utf-8') as f:
                    content = f.read()
                
                with open('.env', 'w', encoding='utf-8') as f:
                    f.write(content)
                
                logger.info("✅ تم إنشاء ملف .env")
                logger.warning("⚠️ يرجى تحديث ملف .env بالمعلومات الصحيحة")
                return True
            except Exception as e:
                logger.error(f"❌ فشل في إنشاء ملف .env: {e}")
                return False
        else:
            logger.error("❌ ملف .env.example غير موجود")
            return False
    else:
        logger.info("✅ ملف .env موجود")
        return True

def fix_network_issues():
    """إصلاح مشاكل الشبكة"""
    logger.info("🔧 إصلاح مشاكل الشبكة...")
    
    try:
        # تشغيل أداة إصلاح الشبكة إذا كانت متوفرة
        if os.path.exists('auto_fix_network.py'):
            logger.info("🔧 تشغيل أداة إصلاح الشبكة...")
            try:
                result = subprocess.run([sys.executable, 'auto_fix_network.py'], 
                                      capture_output=True, text=True, timeout=60)
                if result.returncode == 0:
                    logger.info("✅ تم إصلاح مشاكل الشبكة")
                else:
                    logger.warning("⚠️ أداة إصلاح الشبكة أبلغت عن مشاكل")
            except subprocess.TimeoutExpired:
                logger.warning("⚠️ انتهت مهلة أداة إصلاح الشبكة")
            except Exception as e:
                logger.warning(f"⚠️ خطأ في تشغيل أداة إصلاح الشبكة: {e}")
        
        # إصلاحات أساسية
        try:
            import socket
            # اختبار DNS
            socket.create_connection(("*******", 53), timeout=10)
            logger.info("✅ DNS يعمل")
            
            # اختبار حل أسماء النطاقات
            socket.gethostbyname("api.telegram.org")
            logger.info("✅ حل أسماء النطاقات يعمل")
            
            return True
        except Exception as e:
            logger.error(f"❌ مشكلة في الشبكة: {e}")
            return False
            
    except Exception as e:
        logger.error(f"❌ خطأ في إصلاح الشبكة: {e}")
        return False

def create_missing_files():
    """إنشاء الملفات المفقودة"""
    logger.info("🔧 فحص الملفات المطلوبة...")
    
    # إنشاء مجلدات مطلوبة
    directories = ['logs', 'temp', 'user_customizations']
    for directory in directories:
        if not os.path.exists(directory):
            try:
                os.makedirs(directory)
                logger.info(f"✅ تم إنشاء مجلد {directory}")
            except Exception as e:
                logger.error(f"❌ فشل في إنشاء مجلد {directory}: {e}")
    
    # إنشاء ملفات JSON مطلوبة
    json_files = {
        'user_channels.json': {},
        'all_users.json': {},
        'admin_processed_mods.json': [],
        'pending_publication.json': [],
        'user_feedback.json': {},
        'user_mods_status.json': {},
        'user_blocked_mods.json': {},
        'user_invitations.json': {},
        'admin_settings.json': {"admin_preview_required": True}
    }
    
    for filename, default_content in json_files.items():
        if not os.path.exists(filename):
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(default_content, f, indent=4, ensure_ascii=False)
                logger.info(f"✅ تم إنشاء {filename}")
            except Exception as e:
                logger.error(f"❌ فشل في إنشاء {filename}: {e}")
    
    return True

def fix_import_issues():
    """إصلاح مشاكل الاستيراد"""
    logger.info("🔧 فحص مشاكل الاستيراد...")
    
    # إنشاء ملفات مفقودة بمحتوى أساسي
    missing_modules = {
        'optimization_config.py': '''# ملف إعدادات التحسين
def optimizer():
    pass

def apply_flask_optimizations():
    pass

def get_optimized_telegram_settings():
    return {}
''',
        'security_config.py': '''# ملف إعدادات الحماية
import logging
logger = logging.getLogger(__name__)

def security_check(func):
    return func

def admin_only(func):
    return func

class InputValidator:
    pass

security_logger = logger
''',
        'secure_config.py': '''# ملف الإعدادات الآمنة
import os

config = {}

def get_bot_token():
    return os.environ.get("BOT_TOKEN", "")

def get_admin_id():
    return os.environ.get("ADMIN_CHAT_ID", "")

def get_supabase_url():
    return os.environ.get("SUPABASE_URL", "")

def get_supabase_key():
    return os.environ.get("SUPABASE_KEY", "")
''',
        'security_enhancements.py': '''# ملف التحسينات الأمنية
import logging
logger = logging.getLogger(__name__)

class SecurityManager:
    def __init__(self):
        pass

class DataSanitizer:
    def __init__(self):
        pass

class EncryptionManager:
    def __init__(self):
        pass

security_manager = SecurityManager()
data_sanitizer = DataSanitizer()
encryption_manager = EncryptionManager()

def enhanced_security_check(func):
    return func

def log_security_event(event, user=None, details=None):
    logger.info(f"Security Event: {event} | User: {user} | Details: {details}")
'''
    }
    
    for filename, content in missing_modules.items():
        if not os.path.exists(filename):
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(content)
                logger.info(f"✅ تم إنشاء {filename}")
            except Exception as e:
                logger.error(f"❌ فشل في إنشاء {filename}: {e}")
    
    return True

def run_quick_fix():
    """تشغيل الإصلاح السريع"""
    logger.info("🚀 بدء الإصلاح السريع...")
    logger.info("=" * 50)
    
    fixes = [
        ("تثبيت الحزم المفقودة", install_missing_packages),
        ("إنشاء ملف .env", create_env_file),
        ("إنشاء الملفات المفقودة", create_missing_files),
        ("إصلاح مشاكل الاستيراد", fix_import_issues),
        ("إصلاح مشاكل الشبكة", fix_network_issues)
    ]
    
    success_count = 0
    for fix_name, fix_func in fixes:
        logger.info(f"\n🔧 {fix_name}...")
        try:
            if fix_func():
                logger.info(f"✅ {fix_name}: نجح")
                success_count += 1
            else:
                logger.warning(f"⚠️ {fix_name}: فشل")
        except Exception as e:
            logger.error(f"❌ {fix_name}: خطأ - {e}")
    
    logger.info("\n" + "=" * 50)
    logger.info(f"📊 النتيجة: {success_count}/{len(fixes)} إصلاحات نجحت")
    
    if success_count >= len(fixes) - 1:  # السماح بفشل إصلاح واحد
        logger.info("🎉 الإصلاح السريع مكتمل!")
        logger.info("📋 يمكنك الآن تشغيل:")
        logger.info("   python test_connection_fixes.py")
        logger.info("   python main.py")
        return True
    else:
        logger.error("❌ فشل في عدة إصلاحات. يرجى مراجعة الأخطاء.")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 أداة الإصلاح السريع للبوت")
    print("=" * 50)
    
    success = run_quick_fix()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
