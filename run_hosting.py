#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل مبسط للاستضافة السحابية
Simplified hosting runner for cloud platforms
"""

import os
import sys
import asyncio
import logging
import requests
from datetime import datetime

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def setup_hosting_environment():
    """إعداد البيئة للاستضافة السحابية"""
    logger.info("🚀 إعداد البيئة للاستضافة السحابية...")
    
    # إعداد متغيرات البيئة الأساسية
    required_env = {
        'BOT_TOKEN': '**********************************************',
        'TELEGRAM_BOT_TOKEN': '**********************************************',
        'SUPABASE_URL': 'https://ytqxxodyecdeosnqoure.supabase.co',
        'SUPABASE_KEY': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4',
        'ADMIN_CHAT_ID': '7513880877',
        'USE_NGROK': 'false',
        'NGROK_ENABLED': 'false',
        'ENVIRONMENT': 'production',
        'DEBUG': 'false',
        'PYTHONUNBUFFERED': '1',
        'PYTHONIOENCODING': 'utf-8',
        'PORT': os.getenv('PORT', '10000'),
        'WEB_SERVER_URL': 'https://1c547fe5.sendaddons.pages.dev'
    }
    
    for key, value in required_env.items():
        if not os.getenv(key):
            os.environ[key] = str(value)
            logger.info(f"✅ تم تعيين {key}")
    
    logger.info("✅ تم إعداد البيئة بنجاح")

def fix_telegram_conflicts():
    """إصلاح مشكلة Telegram getUpdates conflicts"""
    try:
        bot_token = os.getenv('BOT_TOKEN')
        if not bot_token:
            logger.warning("⚠️ لم يتم العثور على token البوت")
            return False
            
        logger.info("🔧 إصلاح مشكلة Telegram getUpdates conflicts...")
        
        # مسح webhook
        try:
            url = f"https://api.telegram.org/bot{bot_token}/deleteWebhook"
            response = requests.post(url, json={"drop_pending_updates": True}, timeout=10)
            if response.status_code == 200 and response.json().get('ok'):
                logger.info("✅ تم مسح webhook للبوت")
        except Exception as e:
            logger.warning(f"⚠️ تحذير في مسح webhook: {e}")
        
        # مسح التحديثات المعلقة
        try:
            url = f"https://api.telegram.org/bot{bot_token}/getUpdates"
            params = {"offset": -1, "limit": 1, "timeout": 0}
            response = requests.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('ok') and result.get('result'):
                    last_update_id = result['result'][-1]['update_id']
                    params['offset'] = last_update_id + 1
                    requests.get(url, params=params, timeout=10)
                    logger.info("✅ تم مسح التحديثات المعلقة")
        except Exception as e:
            logger.warning(f"⚠️ تحذير في مسح التحديثات: {e}")
            
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في إصلاح Telegram: {e}")
        return False

def start_web_server():
    """تشغيل خادم الويب البسيط"""
    try:
        from flask import Flask, jsonify
        
        app = Flask(__name__)
        
        @app.route('/')
        def health_check():
            return jsonify({
                "status": "healthy",
                "service": "minecraft_mods_bot",
                "timestamp": str(datetime.now()),
                "message": "Bot is running successfully"
            })
        
        @app.route('/status')
        def bot_status():
            return jsonify({
                "bot_running": True,
                "environment": os.getenv("ENVIRONMENT", "production"),
                "platform": "cloud_hosting"
            })
        
        port = int(os.getenv('PORT', 10000))
        logger.info(f"🌐 تشغيل خادم الويب على المنفذ {port}")
        
        # تشغيل الخادم في thread منفصل
        import threading
        server_thread = threading.Thread(
            target=lambda: app.run(host='0.0.0.0', port=port, debug=False)
        )
        server_thread.daemon = True
        server_thread.start()
        
        logger.info("✅ تم تشغيل خادم الويب بنجاح")
        return True
        
    except Exception as e:
        logger.warning(f"⚠️ فشل في تشغيل خادم الويب: {e}")
        return False

async def run_bot():
    """تشغيل البوت الرئيسي"""
    try:
        logger.info("🤖 بدء تشغيل البوت الرئيسي...")
        
        # استيراد وتشغيل البوت
        import main
        
        # إذا كان للبوت دالة main async
        if hasattr(main, 'main') and asyncio.iscoroutinefunction(main.main):
            await main.main()
        elif hasattr(main, 'main'):
            main.main()
        else:
            logger.info("✅ تم استيراد البوت بنجاح")
            
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل البوت: {e}")
        raise

async def main():
    """الدالة الرئيسية"""
    try:
        logger.info("🚀 بدء تشغيل بوت Minecraft Mods للاستضافة السحابية")
        logger.info(f"⏰ الوقت: {datetime.now()}")
        
        # 1. إعداد البيئة
        setup_hosting_environment()
        
        # 2. إصلاح مشاكل Telegram
        fix_telegram_conflicts()
        
        # 3. تشغيل خادم الويب
        start_web_server()
        
        # 4. تشغيل البوت
        await run_bot()
        
    except KeyboardInterrupt:
        logger.info("⏹️ تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل البوت: {e}")
        raise

def sync_main():
    """دالة تشغيل متزامنة للاستضافة"""
    try:
        # إعداد البيئة
        setup_hosting_environment()
        
        # إصلاح مشاكل Telegram
        fix_telegram_conflicts()
        
        # تشغيل خادم الويب
        start_web_server()
        
        # تشغيل البوت
        logger.info("🤖 تشغيل البوت...")
        
        # تشغيل البوت الرئيسي
        import main
        
        # إذا كان البوت يحتاج asyncio
        if hasattr(main, 'main') and asyncio.iscoroutinefunction(main.main):
            asyncio.run(main.main())
        elif hasattr(main, 'main'):
            main.main()
        else:
            logger.info("✅ تم تشغيل البوت بنجاح")
            
    except Exception as e:
        logger.error(f"❌ خطأ في التشغيل: {e}")
        raise

if __name__ == "__main__":
    try:
        # محاولة التشغيل async أولاً
        asyncio.run(main())
    except Exception as e:
        logger.warning(f"⚠️ فشل التشغيل async، محاولة التشغيل المتزامن: {e}")
        try:
            sync_main()
        except Exception as sync_error:
            logger.error(f"❌ فشل في جميع طرق التشغيل: {sync_error}")
            sys.exit(1)
