-- ===================================================================
-- قاعدة بيانات بوت نشر مودات ماين كرافت
-- تم إنشاؤها لحل مشاكل التخزين المحلي وضمان سلامة البيانات
-- ===================================================================

-- جدول المستخدمين الرئيسي
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(50) UNIQUE NOT NULL,
    username VARCHAR(255),
    full_name VARCHAR(255),
    lang VARCHAR(10) DEFAULT 'ar',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول القنوات
CREATE TABLE IF NOT EXISTS user_channels (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    channel_id VARCHAR(255) NOT NULL,
    channel_name VARCHAR(255),
    is_default BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    publish_interval INTEGER DEFAULT 60,
    channel_lang VARCHAR(10) DEFAULT 'ar',
    preview_enabled BOOLEAN DEFAULT false,
    last_publish_time TIMESTAMP,
    message_format VARCHAR(50) DEFAULT 'classic',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    UNIQUE(user_id, channel_id)
);

-- جدول فئات المودات المسموحة لكل قناة
CREATE TABLE IF NOT EXISTS channel_mod_categories (
    id SERIAL PRIMARY KEY,
    channel_id INTEGER NOT NULL,
    category VARCHAR(100) NOT NULL,
    FOREIGN KEY (channel_id) REFERENCES user_channels(id) ON DELETE CASCADE,
    UNIQUE(channel_id, category)
);

-- جدول إصدارات ماين كرافت المسموحة لكل قناة
CREATE TABLE IF NOT EXISTS channel_mc_versions (
    id SERIAL PRIMARY KEY,
    channel_id INTEGER NOT NULL,
    mc_version VARCHAR(50) NOT NULL,
    FOREIGN KEY (channel_id) REFERENCES user_channels(id) ON DELETE CASCADE,
    UNIQUE(channel_id, mc_version)
);

-- جدول إعدادات تخصيص الصفحة
CREATE TABLE IF NOT EXISTS page_customizations (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    download_button_text_ar VARCHAR(255) DEFAULT 'تحميل',
    download_button_text_en VARCHAR(255) DEFAULT 'Download',
    page_title_ar VARCHAR(255),
    page_title_en VARCHAR(255),
    page_description_ar TEXT,
    page_description_en TEXT,
    custom_css TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    UNIQUE(user_id)
);

-- جدول المودات
CREATE TABLE IF NOT EXISTS mods (
    id SERIAL PRIMARY KEY,
    mod_id INTEGER UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    mc_version VARCHAR(50),
    download_link TEXT,
    image_url TEXT,
    file_size VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true
);

-- جدول المودات المحظورة لكل مستخدم
CREATE TABLE IF NOT EXISTS user_blocked_mods (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    mod_id INTEGER NOT NULL,
    blocked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (mod_id) REFERENCES mods(mod_id) ON DELETE CASCADE,
    UNIQUE(user_id, mod_id)
);

-- جدول المنشورات المعلقة
CREATE TABLE IF NOT EXISTS pending_publications (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    channel_id VARCHAR(255) NOT NULL,
    mod_id INTEGER NOT NULL,
    status VARCHAR(50) NOT NULL, -- 'awaiting_user_approval', 'awaiting_admin_approval', 'approved', 'rejected'
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    approved_at TIMESTAMP,
    approved_by VARCHAR(50),
    rejection_reason TEXT,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (mod_id) REFERENCES mods(mod_id) ON DELETE CASCADE
);

-- جدول إحصائيات النشر
CREATE TABLE IF NOT EXISTS publication_stats (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    channel_id VARCHAR(255) NOT NULL,
    mod_id INTEGER NOT NULL,
    published_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    message_id VARCHAR(50),
    views_count INTEGER DEFAULT 0,
    downloads_count INTEGER DEFAULT 0,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (mod_id) REFERENCES mods(mod_id) ON DELETE CASCADE
);

-- جدول الدعوات
CREATE TABLE IF NOT EXISTS user_invitations (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    invitation_code VARCHAR(100) UNIQUE NOT NULL,
    is_used BOOLEAN DEFAULT false,
    used_by VARCHAR(50),
    used_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (used_by) REFERENCES users(user_id) ON DELETE SET NULL
);

-- جدول سجل الأنشطة
CREATE TABLE IF NOT EXISTS activity_logs (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    action VARCHAR(100) NOT NULL,
    details TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
);

-- ===================================================================
-- الفهارس لتحسين الأداء
-- ===================================================================

-- فهارس للبحث السريع
CREATE INDEX IF NOT EXISTS idx_users_user_id ON users(user_id);
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_last_activity ON users(last_activity);

CREATE INDEX IF NOT EXISTS idx_user_channels_user_id ON user_channels(user_id);
CREATE INDEX IF NOT EXISTS idx_user_channels_channel_id ON user_channels(channel_id);
CREATE INDEX IF NOT EXISTS idx_user_channels_is_default ON user_channels(is_default);
CREATE INDEX IF NOT EXISTS idx_user_channels_is_active ON user_channels(is_active);

CREATE INDEX IF NOT EXISTS idx_mods_mod_id ON mods(mod_id);
CREATE INDEX IF NOT EXISTS idx_mods_category ON mods(category);
CREATE INDEX IF NOT EXISTS idx_mods_mc_version ON mods(mc_version);
CREATE INDEX IF NOT EXISTS idx_mods_is_active ON mods(is_active);

CREATE INDEX IF NOT EXISTS idx_pending_publications_user_id ON pending_publications(user_id);
CREATE INDEX IF NOT EXISTS idx_pending_publications_status ON pending_publications(status);
CREATE INDEX IF NOT EXISTS idx_pending_publications_mod_id ON pending_publications(mod_id);

CREATE INDEX IF NOT EXISTS idx_publication_stats_user_id ON publication_stats(user_id);
CREATE INDEX IF NOT EXISTS idx_publication_stats_channel_id ON publication_stats(channel_id);
CREATE INDEX IF NOT EXISTS idx_publication_stats_published_at ON publication_stats(published_at);

CREATE INDEX IF NOT EXISTS idx_user_invitations_user_id ON user_invitations(user_id);
CREATE INDEX IF NOT EXISTS idx_user_invitations_code ON user_invitations(invitation_code);
CREATE INDEX IF NOT EXISTS idx_user_invitations_is_used ON user_invitations(is_used);

CREATE INDEX IF NOT EXISTS idx_activity_logs_user_id ON activity_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_activity_logs_action ON activity_logs(action);
CREATE INDEX IF NOT EXISTS idx_activity_logs_created_at ON activity_logs(created_at);

-- ===================================================================
-- البيانات الافتراضية
-- ===================================================================

-- إدراج فئات المودات الافتراضية (سيتم استخدامها كمرجع)
INSERT INTO mods (mod_id, name, category, description, mc_version, is_active) VALUES 
(0, 'Default Categories Reference', 'system', 'Reference for default mod categories', '1.21+', false)
ON CONFLICT (mod_id) DO NOTHING;

-- ===================================================================
-- دوال مساعدة (اختيارية)
-- ===================================================================

-- دالة لتحديث timestamp عند التعديل
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- تطبيق الدالة على الجداول المناسبة
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_channels_updated_at BEFORE UPDATE ON user_channels 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_page_customizations_updated_at BEFORE UPDATE ON page_customizations 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_mods_updated_at BEFORE UPDATE ON mods 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_pending_publications_updated_at BEFORE UPDATE ON pending_publications 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
