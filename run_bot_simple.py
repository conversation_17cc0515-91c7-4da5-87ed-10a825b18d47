#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل البوت البسيط
يشغل البوت مباشرة مع إصلاح مشكلة Telegram Conflict
"""

import os
import sys
import time
import logging
import requests
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

def clear_telegram_updates():
    """مسح التحديثات المعلقة في Telegram"""
    try:
        bot_token = os.getenv('BOT_TOKEN')
        if not bot_token:
            logger.error("❌ لم يتم العثور على BOT_TOKEN")
            return False
        
        logger.info("🔧 مسح التحديثات المعلقة في Telegram...")
        
        # مسح webhook أولاً
        webhook_url = f"https://api.telegram.org/bot{bot_token}/deleteWebhook"
        try:
            response = requests.post(webhook_url, json={"drop_pending_updates": True}, timeout=10)
            if response.status_code == 200:
                logger.info("✅ تم مسح webhook")
        except Exception as e:
            logger.warning(f"تحذير في مسح webhook: {e}")
        
        # مسح جميع التحديثات المعلقة
        updates_url = f"https://api.telegram.org/bot{bot_token}/getUpdates"
        
        try:
            # جلب التحديثات ومسحها
            for attempt in range(5):
                response = requests.get(updates_url, params={"timeout": 1, "limit": 100}, timeout=15)
                if response.status_code == 200:
                    result = response.json()
                    if result.get('ok') and result.get('result'):
                        updates = result['result']
                        if updates:
                            # مسح التحديثات
                            last_id = updates[-1]['update_id']
                            requests.get(updates_url, params={"offset": last_id + 1, "timeout": 1}, timeout=10)
                            logger.info(f"✅ تم مسح {len(updates)} تحديث معلق")
                        else:
                            logger.info("✅ لا توجد تحديثات معلقة")
                            break
                    else:
                        break
                else:
                    logger.warning(f"فشل في جلب التحديثات: {response.status_code}")
                    break
        except Exception as e:
            logger.warning(f"تحذير في مسح التحديثات: {e}")
        
        # انتظار قصير
        time.sleep(2)
        logger.info("✅ تم مسح جميع التحديثات المعلقة")
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في مسح التحديثات: {e}")
        return False

def check_environment():
    """فحص متغيرات البيئة"""
    logger.info("🔍 فحص متغيرات البيئة...")
    
    required_vars = ['BOT_TOKEN', 'ADMIN_CHAT_ID', 'SUPABASE_URL', 'SUPABASE_KEY']
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
        else:
            logger.info(f"✅ {var}: موجود")
    
    if missing_vars:
        logger.error(f"❌ متغيرات البيئة المفقودة: {', '.join(missing_vars)}")
        return False
    
    logger.info("✅ جميع متغيرات البيئة متوفرة")
    return True

def main():
    """الدالة الرئيسية"""
    logger.info("🤖 بدء تشغيل البوت...")
    logger.info("=" * 50)
    
    # 1. فحص متغيرات البيئة
    if not check_environment():
        logger.error("❌ فشل في فحص متغيرات البيئة")
        return
    
    # 2. مسح التحديثات المعلقة
    clear_telegram_updates()
    
    # 3. انتظار قصير
    logger.info("⏳ انتظار 3 ثوانِ قبل التشغيل...")
    time.sleep(3)
    
    # 4. تشغيل البوت
    logger.info("🚀 تشغيل البوت الآن...")
    logger.info("=" * 50)
    
    try:
        # استيراد وتشغيل البوت
        import main
        
    except KeyboardInterrupt:
        logger.info("🛑 تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل البوت: {e}")
        
        # في حالة خطأ Telegram Conflict، محاولة الإصلاح
        if "Conflict" in str(e) or "getUpdates" in str(e):
            logger.info("🔧 محاولة إصلاح مشكلة Telegram Conflict...")
            clear_telegram_updates()
            time.sleep(5)
            logger.info("🔄 إعادة محاولة تشغيل البوت...")
            try:
                import importlib
                importlib.reload(main)
            except Exception as e2:
                logger.error(f"❌ فشل في إعادة التشغيل: {e2}")

if __name__ == "__main__":
    main()
