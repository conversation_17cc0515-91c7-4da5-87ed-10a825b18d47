# ===================================================================
# نظام الحماية المتقدم للبوت - Advanced Security System
# ===================================================================
# نظام حماية شامل ضد الهجمات والاختراق والتلاعب بالبيانات
# مصمم خصيصاً للحماية من المنافسين ومحاولات التدمير
# ===================================================================

import os
import sys
import time
import json
import hashlib
import hmac
import logging
import threading
import ipaddress
from datetime import datetime, timezone, timedelta
from collections import defaultdict, deque
from typing import Dict, List, Set, Optional, Any
import requests
from functools import wraps
import asyncio
import telegram
from telegram import Update
from telegram.ext import ContextTypes

# إعداد التسجيل الأمني
security_logger = logging.getLogger('security')
security_handler = logging.FileHandler('security/advanced_security.log', encoding='utf-8')
security_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
security_handler.setFormatter(security_formatter)
security_logger.addHandler(security_handler)
security_logger.setLevel(logging.INFO)

# ===================================================================
# إعدادات الحماية
# ===================================================================

class SecurityConfig:
    # حدود معدل الطلبات
    MAX_REQUESTS_PER_MINUTE = 30
    MAX_REQUESTS_PER_HOUR = 200
    MAX_REQUESTS_PER_DAY = 1000
    
    # حدود الرسائل
    MAX_MESSAGE_LENGTH = 4000
    MAX_MESSAGES_PER_MINUTE = 20
    MAX_COMMANDS_PER_MINUTE = 10
    
    # حدود البيانات
    MAX_CHANNEL_NAME_LENGTH = 100
    MAX_USERNAME_LENGTH = 50
    MAX_FEEDBACK_LENGTH = 500
    
    # فترات الحظر
    TEMP_BAN_DURATION = 300  # 5 دقائق
    LONG_BAN_DURATION = 3600  # ساعة واحدة
    PERMANENT_BAN_DURATION = 86400 * 7  # أسبوع
    
    # عتبات التحذير
    SUSPICIOUS_ACTIVITY_THRESHOLD = 5
    ATTACK_THRESHOLD = 10
    
    # قائمة المستخدمين المحظورين
    BLOCKED_USER_IDS = set()
    
    # قائمة IP المحظورة (للاستضافة)
    BLOCKED_IPS = set()
    
    # أنماط مشبوهة
    SUSPICIOUS_PATTERNS = [
        r'<script.*?>.*?</script>',
        r'javascript:',
        r'data:text/html',
        r'vbscript:',
        r'onload=',
        r'onerror=',
        r'eval\(',
        r'document\.cookie',
        r'window\.location',
        r'alert\(',
        r'confirm\(',
        r'prompt\(',
        r'iframe',
        r'embed',
        r'object',
        r'applet',
        r'meta.*refresh',
        r'\.\./',
        r'file://',
        r'ftp://',
        r'ldap://',
        r'gopher://',
        r'dict://',
        r'php://',
        r'expect://',
        r'zip://',
        r'data://',
        r'glob://',
        r'phar://',
        r'ssh2://',
        r'rar://',
        r'ogg://',
        r'zlib://',
        r'bzip2://'
    ]

# ===================================================================
# مدير الحماية المتقدم
# ===================================================================

class AdvancedSecurityManager:
    def __init__(self):
        self.user_activity = defaultdict(lambda: {
            'requests': deque(),
            'messages': deque(),
            'commands': deque(),
            'warnings': 0,
            'last_activity': None,
            'banned_until': None,
            'suspicious_count': 0,
            'total_requests': 0,
            'failed_attempts': 0
        })
        
        self.ip_activity = defaultdict(lambda: {
            'requests': deque(),
            'warnings': 0,
            'banned_until': None
        })
        
        self.session_tokens = {}
        self.active_sessions = set()
        self.failed_logins = defaultdict(int)
        
        # خيوط التنظيف
        self.cleanup_thread = threading.Thread(target=self._cleanup_old_data, daemon=True)
        self.cleanup_thread.start()
        
        # تحميل البيانات المحفوظة
        self._load_security_data()
        
        security_logger.info("🛡️ تم تهيئة نظام الحماية المتقدم")

    def _cleanup_old_data(self):
        """تنظيف البيانات القديمة بشكل دوري"""
        while True:
            try:
                current_time = time.time()
                
                # تنظيف بيانات النشاط القديمة
                for user_id in list(self.user_activity.keys()):
                    user_data = self.user_activity[user_id]
                    
                    # إزالة الطلبات القديمة
                    while user_data['requests'] and current_time - user_data['requests'][0] > 86400:
                        user_data['requests'].popleft()
                    
                    # إزالة الرسائل القديمة
                    while user_data['messages'] and current_time - user_data['messages'][0] > 3600:
                        user_data['messages'].popleft()
                    
                    # إزالة الأوامر القديمة
                    while user_data['commands'] and current_time - user_data['commands'][0] > 3600:
                        user_data['commands'].popleft()
                    
                    # إزالة المستخدمين غير النشطين
                    if (not user_data['requests'] and not user_data['messages'] and 
                        not user_data['commands'] and user_data['warnings'] == 0):
                        del self.user_activity[user_id]
                
                # تنظيف بيانات IP القديمة
                for ip in list(self.ip_activity.keys()):
                    ip_data = self.ip_activity[ip]
                    while ip_data['requests'] and current_time - ip_data['requests'][0] > 86400:
                        ip_data['requests'].popleft()
                    
                    if not ip_data['requests'] and ip_data['warnings'] == 0:
                        del self.ip_activity[ip]
                
                # حفظ البيانات
                self._save_security_data()
                
                time.sleep(300)  # تنظيف كل 5 دقائق
                
            except Exception as e:
                security_logger.error(f"خطأ في تنظيف البيانات: {e}")
                time.sleep(60)

    def _load_security_data(self):
        """تحميل بيانات الحماية المحفوظة"""
        try:
            if os.path.exists('security/security_data.json'):
                with open('security/security_data.json', 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                    # تحميل المستخدمين المحظورين
                    SecurityConfig.BLOCKED_USER_IDS.update(data.get('blocked_users', []))
                    
                    # تحميل IP المحظورة
                    SecurityConfig.BLOCKED_IPS.update(data.get('blocked_ips', []))
                    
                    security_logger.info("✅ تم تحميل بيانات الحماية المحفوظة")
        except Exception as e:
            security_logger.error(f"خطأ في تحميل بيانات الحماية: {e}")

    def _save_security_data(self):
        """حفظ بيانات الحماية"""
        try:
            os.makedirs('security', exist_ok=True)
            
            data = {
                'blocked_users': list(SecurityConfig.BLOCKED_USER_IDS),
                'blocked_ips': list(SecurityConfig.BLOCKED_IPS),
                'last_update': datetime.now().isoformat()
            }
            
            with open('security/security_data.json', 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            security_logger.error(f"خطأ في حفظ بيانات الحماية: {e}")

    def is_user_banned(self, user_id: str) -> bool:
        """التحقق من حظر المستخدم"""
        if str(user_id) in SecurityConfig.BLOCKED_USER_IDS:
            return True
        
        user_data = self.user_activity[str(user_id)]
        if user_data['banned_until'] and time.time() < user_data['banned_until']:
            return True
        
        return False

    def ban_user(self, user_id: str, duration: int = None, reason: str = "مخالفة أمنية"):
        """حظر مستخدم"""
        user_id = str(user_id)
        
        if duration is None:
            duration = SecurityConfig.TEMP_BAN_DURATION
        
        if duration == -1:  # حظر دائم
            SecurityConfig.BLOCKED_USER_IDS.add(user_id)
            self.user_activity[user_id]['banned_until'] = None
        else:
            self.user_activity[user_id]['banned_until'] = time.time() + duration
        
        security_logger.warning(f"🚫 تم حظر المستخدم {user_id} لمدة {duration} ثانية. السبب: {reason}")
        self._save_security_data()

    def unban_user(self, user_id: str):
        """إلغاء حظر مستخدم"""
        user_id = str(user_id)
        
        SecurityConfig.BLOCKED_USER_IDS.discard(user_id)
        self.user_activity[user_id]['banned_until'] = None
        self.user_activity[user_id]['warnings'] = 0
        self.user_activity[user_id]['suspicious_count'] = 0
        
        security_logger.info(f"✅ تم إلغاء حظر المستخدم {user_id}")
        self._save_security_data()

    def check_rate_limit(self, user_id: str, request_type: str = 'request') -> bool:
        """فحص حدود معدل الطلبات"""
        user_id = str(user_id)
        current_time = time.time()
        user_data = self.user_activity[user_id]
        
        if request_type == 'request':
            requests_queue = user_data['requests']
            max_per_minute = SecurityConfig.MAX_REQUESTS_PER_MINUTE
        elif request_type == 'message':
            requests_queue = user_data['messages']
            max_per_minute = SecurityConfig.MAX_MESSAGES_PER_MINUTE
        elif request_type == 'command':
            requests_queue = user_data['commands']
            max_per_minute = SecurityConfig.MAX_COMMANDS_PER_MINUTE
        else:
            return True
        
        # إزالة الطلبات القديمة (أكثر من دقيقة)
        while requests_queue and current_time - requests_queue[0] > 60:
            requests_queue.popleft()
        
        # فحص الحد الأقصى
        if len(requests_queue) >= max_per_minute:
            self._handle_rate_limit_violation(user_id, request_type)
            return False
        
        # إضافة الطلب الحالي
        requests_queue.append(current_time)
        user_data['total_requests'] += 1
        user_data['last_activity'] = current_time
        
        return True

    def _handle_rate_limit_violation(self, user_id: str, request_type: str):
        """معالجة انتهاك حدود المعدل"""
        user_data = self.user_activity[user_id]
        user_data['warnings'] += 1
        user_data['suspicious_count'] += 1
        
        security_logger.warning(f"⚠️ انتهاك حدود المعدل: المستخدم {user_id}, النوع: {request_type}, التحذيرات: {user_data['warnings']}")
        
        # تطبيق العقوبات التدريجية
        if user_data['warnings'] >= SecurityConfig.ATTACK_THRESHOLD:
            self.ban_user(user_id, SecurityConfig.PERMANENT_BAN_DURATION, "انتهاكات متكررة لحدود المعدل")
        elif user_data['warnings'] >= SecurityConfig.SUSPICIOUS_ACTIVITY_THRESHOLD:
            self.ban_user(user_id, SecurityConfig.LONG_BAN_DURATION, "نشاط مشبوه")
        elif user_data['warnings'] >= 3:
            self.ban_user(user_id, SecurityConfig.TEMP_BAN_DURATION, "انتهاك حدود المعدل")

    def validate_input(self, text: str, input_type: str = 'general') -> bool:
        """التحقق من صحة المدخلات وفحص الأنماط المشبوهة"""
        if not text or not isinstance(text, str):
            return False
        
        # فحص الطول
        max_length = {
            'message': SecurityConfig.MAX_MESSAGE_LENGTH,
            'channel_name': SecurityConfig.MAX_CHANNEL_NAME_LENGTH,
            'username': SecurityConfig.MAX_USERNAME_LENGTH,
            'feedback': SecurityConfig.MAX_FEEDBACK_LENGTH,
            'general': SecurityConfig.MAX_MESSAGE_LENGTH
        }.get(input_type, SecurityConfig.MAX_MESSAGE_LENGTH)
        
        if len(text) > max_length:
            return False
        
        # فحص الأنماط المشبوهة
        import re
        for pattern in SecurityConfig.SUSPICIOUS_PATTERNS:
            if re.search(pattern, text, re.IGNORECASE):
                security_logger.warning(f"🚨 نمط مشبوه تم اكتشافه: {pattern} في النص: {text[:100]}...")
                return False
        
        return True

    def log_suspicious_activity(self, user_id: str, activity: str, details: str = ""):
        """تسجيل النشاط المشبوه"""
        user_id = str(user_id)
        user_data = self.user_activity[user_id]
        user_data['suspicious_count'] += 1
        
        security_logger.warning(f"🚨 نشاط مشبوه: المستخدم {user_id}, النشاط: {activity}, التفاصيل: {details}")
        
        # اتخاذ إجراءات إذا تجاوز العتبة
        if user_data['suspicious_count'] >= SecurityConfig.SUSPICIOUS_ACTIVITY_THRESHOLD:
            self.ban_user(user_id, SecurityConfig.LONG_BAN_DURATION, f"نشاط مشبوه متكرر: {activity}")

    def check_admin_access(self, user_id: str, admin_id: str) -> bool:
        """التحقق من صلاحيات المسؤول"""
        if str(user_id) != str(admin_id):
            self.log_suspicious_activity(user_id, "محاولة وصول غير مصرح بها لوظائف المسؤول")
            return False
        return True

    def encrypt_sensitive_data(self, data: str, key: str = None) -> str:
        """تشفير البيانات الحساسة"""
        try:
            if key is None:
                key = os.environ.get('ENCRYPTION_KEY', 'default_key_change_me')
            
            # تشفير بسيط باستخدام XOR
            encrypted = ""
            for i, char in enumerate(data):
                encrypted += chr(ord(char) ^ ord(key[i % len(key)]))
            
            # تحويل إلى base64
            import base64
            return base64.b64encode(encrypted.encode('utf-8')).decode('utf-8')
            
        except Exception as e:
            security_logger.error(f"خطأ في تشفير البيانات: {e}")
            return data

    def decrypt_sensitive_data(self, encrypted_data: str, key: str = None) -> str:
        """فك تشفير البيانات الحساسة"""
        try:
            if key is None:
                key = os.environ.get('ENCRYPTION_KEY', 'default_key_change_me')
            
            # فك التشفير من base64
            import base64
            encrypted = base64.b64decode(encrypted_data.encode('utf-8')).decode('utf-8')
            
            # فك التشفير XOR
            decrypted = ""
            for i, char in enumerate(encrypted):
                decrypted += chr(ord(char) ^ ord(key[i % len(key)]))
            
            return decrypted
            
        except Exception as e:
            security_logger.error(f"خطأ في فك تشفير البيانات: {e}")
            return encrypted_data

    def generate_secure_token(self, user_id: str) -> str:
        """إنشاء رمز أمان للجلسة"""
        import secrets
        import hashlib
        
        timestamp = str(int(time.time()))
        random_data = secrets.token_hex(16)
        
        # إنشاء hash آمن
        token_data = f"{user_id}:{timestamp}:{random_data}"
        token = hashlib.sha256(token_data.encode()).hexdigest()
        
        # حفظ الرمز
        self.session_tokens[token] = {
            'user_id': user_id,
            'created_at': time.time(),
            'expires_at': time.time() + 3600  # ساعة واحدة
        }
        
        return token

    def validate_token(self, token: str, user_id: str) -> bool:
        """التحقق من صحة رمز الجلسة"""
        if token not in self.session_tokens:
            return False
        
        token_data = self.session_tokens[token]
        
        # فحص انتهاء الصلاحية
        if time.time() > token_data['expires_at']:
            del self.session_tokens[token]
            return False
        
        # فحص المستخدم
        if token_data['user_id'] != str(user_id):
            return False
        
        return True

    def get_security_report(self) -> Dict[str, Any]:
        """إنشاء تقرير أمني"""
        current_time = time.time()
        
        total_users = len(self.user_activity)
        banned_users = len(SecurityConfig.BLOCKED_USER_IDS)
        temp_banned = sum(1 for data in self.user_activity.values() 
                         if data['banned_until'] and data['banned_until'] > current_time)
        
        suspicious_users = sum(1 for data in self.user_activity.values() 
                              if data['suspicious_count'] > 0)
        
        total_requests = sum(data['total_requests'] for data in self.user_activity.values())
        
        return {
            'timestamp': datetime.now().isoformat(),
            'total_users': total_users,
            'banned_users': banned_users,
            'temp_banned_users': temp_banned,
            'suspicious_users': suspicious_users,
            'total_requests': total_requests,
            'active_sessions': len(self.session_tokens),
            'blocked_ips': len(SecurityConfig.BLOCKED_IPS)
        }

# ===================================================================
# مدير الحماية العالمي
# ===================================================================

# إنشاء مثيل عالمي
security_manager = AdvancedSecurityManager()

# ===================================================================
# ديكوريتر الحماية
# ===================================================================

def security_check(request_type: str = 'request', admin_only: bool = False):
    """ديكوريتر للفحص الأمني"""
    def decorator(func):
        @wraps(func)
        async def wrapper(update: Update, context: ContextTypes.DEFAULT_TYPE, *args, **kwargs):
            user_id = str(update.effective_user.id)
            
            # فحص الحظر
            if security_manager.is_user_banned(user_id):
                security_logger.warning(f"🚫 محاولة وصول من مستخدم محظور: {user_id}")
                await update.message.reply_text("❌ تم حظرك من استخدام البوت.")
                return
            
            # فحص صلاحيات المسؤول
            if admin_only:
                admin_id = os.environ.get('ADMIN_CHAT_ID', '')
                if not security_manager.check_admin_access(user_id, admin_id):
                    await update.message.reply_text("❌ ليس لديك صلاحية للوصول لهذه الوظيفة.")
                    return
            
            # فحص حدود المعدل
            if not security_manager.check_rate_limit(user_id, request_type):
                await update.message.reply_text("⚠️ تم تجاوز حد الطلبات المسموح. يرجى المحاولة لاحقاً.")
                return
            
            # تنفيذ الدالة الأصلية
            return await func(update, context, *args, **kwargs)
        
        return wrapper
    return decorator

def validate_input_security(input_type: str = 'general'):
    """ديكوريتر للتحقق من صحة المدخلات"""
    def decorator(func):
        @wraps(func)
        async def wrapper(update: Update, context: ContextTypes.DEFAULT_TYPE, *args, **kwargs):
            user_id = str(update.effective_user.id)
            
            # فحص النص المرسل
            if update.message and update.message.text:
                if not security_manager.validate_input(update.message.text, input_type):
                    security_manager.log_suspicious_activity(
                        user_id, 
                        "إدخال مشبوه", 
                        f"نوع: {input_type}, النص: {update.message.text[:100]}..."
                    )
                    await update.message.reply_text("❌ تم اكتشاف محتوى مشبوه في رسالتك.")
                    return
            
            return await func(update, context, *args, **kwargs)
        
        return wrapper
    return decorator
