#!/bin/bash
# ملف تشغيل البوت على Render
# Start script for Render deployment

echo "🚀 بدء تشغيل البوت على Render..."
echo "🚀 Starting bot on Render..."

# تعيين متغيرات البيئة الأساسية
export RENDER=true
export PYTHONUNBUFFERED=1
export PYTHONIOENCODING=utf-8

# التأكد من وجود Python
if command -v python3 &> /dev/null; then
    PYTHON_CMD=python3
elif command -v python &> /dev/null; then
    PYTHON_CMD=python
else
    echo "❌ Python غير موجود!"
    echo "❌ Python not found!"
    exit 1
fi

echo "✅ استخدام: $PYTHON_CMD"
echo "✅ Using: $PYTHON_CMD"

# إنشاء المجلدات المطلوبة
echo "📁 إنشاء المجلدات المطلوبة..."
echo "📁 Creating required directories..."

mkdir -p logs
mkdir -p temp
mkdir -p cache
mkdir -p security/logs
mkdir -p security/quarantine
mkdir -p security_logs

echo "✅ تم إنشاء المجلدات"
echo "✅ Directories created"

# تشغيل البوت
echo "🤖 تشغيل البوت..."
echo "🤖 Starting bot..."

exec $PYTHON_CMD start_render.py
