# ملفات البيئة والإعدادات الحساسة
.env
.env.local
.env.production
.env.staging
config.json
secrets.json

# ملفات Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# ملفات السجلات
logs/
*.log
*.log.*

# ملفات مؤقتة
temp/
tmp/
*.tmp
*.temp

# ملفات النظام
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# ملفات IDE
.vscode/
.idea/
*.swp
*.swo
*~

# ملفات قواعد البيانات المحلية
*.db
*.sqlite
*.sqlite3

# ملفات النسخ الاحتياطي
*.backup
*.bak
*.old

# ملفات الصور المرفوعة
uploaded_images/
user_uploads/

# ملفات التخصيص المحلية
user_customizations/*.json
!user_customizations/.gitkeep

# ملفات الشبكة
ngrok
ngrok.exe
*.ngrok

# ملفات Docker
.dockerignore

# ملفات Node.js (إذا كانت موجودة)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# ملفات التطوير
.pytest_cache/
.coverage
htmlcov/
.tox/
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# ملفات الإنتاج
*.pid
*.seed
*.pid.lock

# ملفات التشفير
*.key
*.pem
*.crt
*.csr

# ملفات الأرشيف
*.zip
*.tar.gz
*.rar
*.7z

# ملفات خاصة بالمشروع
mods.json
all_users.json
broadcast_history.json
pending_publications.json
custom_download_links.json
notification_templates.json
user_*.json
