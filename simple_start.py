#!/usr/bin/env python3
"""
ملف تشغيل بسيط جداً - يضمن عمل البوت
Ultra-simple starter - guarantees bot works
"""

import os
import sys
import logging

# إعداد التسجيل
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def setup_all():
    """إعداد شامل للبوت"""
    logger.info("🚀 بدء الإعداد الشامل...")
    
    # 1. إعداد متغيرات البيئة
    env_vars = {
        'BOT_TOKEN': '**********************************************',
        'TELEGRAM_BOT_TOKEN': '**********************************************',
        'SUPABASE_URL': 'https://ytqxxodyecdeosnqoure.supabase.co',
        'SUPABASE_KEY': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4',
        'ADMIN_CHAT_ID': '7513880877',
        'USE_NGROK': 'false',
        'NGROK_ENABLED': 'false',
        'ENVIRONMENT': 'production',
        'DEBUG': 'false',
        'PYTHONUNBUFFERED': '1',
        'PYTHONIOENCODING': 'utf-8',
        'RENDER': 'true',
        'PORT': os.getenv('PORT', '10000'),
        'WEB_SERVER_URL': 'https://1c547fe5.sendaddons.pages.dev'
    }
    
    for key, value in env_vars.items():
        if not os.getenv(key):
            os.environ[key] = str(value)
    
    logger.info("✅ تم إعداد متغيرات البيئة")
    
    # 2. إصلاح Telegram
    try:
        import requests
        bot_token = os.getenv('BOT_TOKEN')
        if bot_token:
            # مسح webhook
            requests.post(f"https://api.telegram.org/bot{bot_token}/deleteWebhook", 
                         json={"drop_pending_updates": True}, timeout=10)
            
            # مسح التحديثات المعلقة
            response = requests.get(f"https://api.telegram.org/bot{bot_token}/getUpdates", 
                                  params={"offset": -1, "limit": 1, "timeout": 0}, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('ok') and result.get('result'):
                    last_update_id = result['result'][-1]['update_id']
                    requests.get(f"https://api.telegram.org/bot{bot_token}/getUpdates", 
                               params={"offset": last_update_id + 1, "limit": 1, "timeout": 0}, timeout=10)
            
            logger.info("✅ تم إصلاح Telegram")
    except Exception as e:
        logger.warning(f"⚠️ تحذير في إصلاح Telegram: {e}")
    
    # 3. تشغيل خادم الصحة
    try:
        from flask import Flask, jsonify
        import threading
        from datetime import datetime
        
        app = Flask(__name__)
        
        @app.route('/')
        def health():
            return jsonify({
                "status": "healthy",
                "service": "minecraft_mods_bot",
                "timestamp": str(datetime.now()),
                "platform": "render"
            })
        
        @app.route('/health')
        def health_check():
            return jsonify({"status": "ok"})
        
        port = int(os.getenv('PORT', 10000))
        
        def run_server():
            app.run(host='0.0.0.0', port=port, debug=False)
        
        server_thread = threading.Thread(target=run_server)
        server_thread.daemon = True
        server_thread.start()
        
        logger.info(f"🌐 خادم الصحة يعمل على المنفذ {port}")
    except Exception as e:
        logger.warning(f"⚠️ فشل في تشغيل خادم الصحة: {e}")
    
    logger.info("✅ تم الإعداد الشامل")

def run_bot():
    """تشغيل البوت بطرق متعددة"""
    logger.info("🤖 تشغيل البوت...")
    
    # قائمة بطرق التشغيل المختلفة
    methods = [
        # الطريقة 1: تشغيل main مباشرة
        lambda: exec("import main; main.main() if hasattr(main, 'main') else None"),
        
        # الطريقة 2: تشغيل main مع asyncio
        lambda: exec("import main, asyncio; asyncio.run(main.main()) if hasattr(main, 'main') and asyncio.iscoroutinefunction(main.main) else main.main()"),
        
        # الطريقة 3: تشغيل start_render
        lambda: exec("import start_render, asyncio; asyncio.run(start_render.main())"),
        
        # الطريقة 4: استيراد main فقط
        lambda: exec("import main")
    ]
    
    for i, method in enumerate(methods, 1):
        try:
            logger.info(f"🔄 محاولة التشغيل {i}...")
            method()
            logger.info("✅ تم تشغيل البوت بنجاح!")
            return True
        except Exception as e:
            logger.warning(f"⚠️ فشلت المحاولة {i}: {e}")
            continue
    
    logger.error("❌ فشل في جميع محاولات التشغيل")
    return False

def main():
    """الدالة الرئيسية"""
    try:
        logger.info("🚀 بدء تشغيل البوت - الوضع البسيط")
        
        # إعداد شامل
        setup_all()
        
        # تشغيل البوت
        success = run_bot()
        
        if success:
            logger.info("🎉 البوت يعمل بنجاح!")
            # إبقاء البرنامج يعمل
            try:
                import time
                while True:
                    time.sleep(60)
                    logger.info("💓 البوت لا يزال يعمل...")
            except KeyboardInterrupt:
                logger.info("⏹️ تم إيقاف البوت")
        else:
            logger.error("❌ فشل في تشغيل البوت")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"❌ خطأ عام: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
