#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة إعداد البوت الشاملة
Complete Bot Setup Tool

هذا الملف يقوم بإعداد البوت بالكامل للاستضافة
This file completely sets up the bot for hosting
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# إعداد التسجيل
logging.basicConfig(
    format="%(asctime)s - %(levelname)s - %(message)s",
    level=logging.INFO,
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger(__name__)

def print_header():
    """طباعة رأس الأداة"""
    print("🤖 أداة إعداد البوت الشاملة")
    print("=" * 50)
    print("📋 هذه الأداة ستقوم بـ:")
    print("   1. تثبيت جميع المكتبات المطلوبة")
    print("   2. التحقق من ملفات الإعداد")
    print("   3. إنشاء الملفات المفقودة")
    print("   4. اختبار الاتصالات")
    print("   5. تجهيز البوت للتشغيل")
    print("=" * 50)

def install_requirements():
    """تثبيت المتطلبات"""
    logger.info("📦 تثبيت المتطلبات...")
    
    try:
        # تشغيل أداة تثبيت المتطلبات
        result = subprocess.run([
            sys.executable, "install_requirements.py"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info("✅ تم تثبيت المتطلبات بنجاح")
            return True
        else:
            logger.error(f"❌ فشل في تثبيت المتطلبات: {result.stderr}")
            return False
            
    except FileNotFoundError:
        logger.warning("⚠️ ملف install_requirements.py غير موجود، محاولة التثبيت المباشر...")
        
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
            ])
            logger.info("✅ تم تثبيت المتطلبات بنجاح")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ فشل في تثبيت المتطلبات: {e}")
            return False

def check_env_file():
    """التحقق من ملف .env"""
    logger.info("🔍 التحقق من ملف .env...")
    
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if env_file.exists():
        logger.info("✅ ملف .env موجود")
        
        # التحقق من المتغيرات المطلوبة
        required_vars = [
            "BOT_TOKEN",
            "ADMIN_CHAT_ID", 
            "SUPABASE_URL",
            "SUPABASE_KEY"
        ]
        
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            missing_vars = []
            for var in required_vars:
                if f"{var}=" not in content:
                    missing_vars.append(var)
            
            if missing_vars:
                logger.warning(f"⚠️ متغيرات مفقودة في .env: {missing_vars}")
                return False
            else:
                logger.info("✅ جميع المتغيرات المطلوبة موجودة")
                return True
                
        except Exception as e:
            logger.error(f"❌ فشل في قراءة ملف .env: {e}")
            return False
    
    elif env_example.exists():
        logger.info("📋 نسخ .env.example إلى .env...")
        try:
            with open(env_example, 'r', encoding='utf-8') as f:
                content = f.read()
            
            with open(env_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            logger.info("✅ تم إنشاء ملف .env")
            logger.warning("⚠️ يرجى تحديث المتغيرات في ملف .env")
            return False
            
        except Exception as e:
            logger.error(f"❌ فشل في نسخ .env.example: {e}")
            return False
    
    else:
        logger.error("❌ ملف .env و .env.example غير موجودين")
        return False

def check_required_files():
    """التحقق من الملفات المطلوبة"""
    logger.info("🔍 التحقق من الملفات المطلوبة...")
    
    required_files = [
        "main.py",
        "supabase_client.py",
        "web_server.py",
        "telegram_web_app.py",
        "requirements.txt"
    ]
    
    missing_files = []
    for file_name in required_files:
        if not Path(file_name).exists():
            missing_files.append(file_name)
        else:
            logger.info(f"✅ {file_name}")
    
    if missing_files:
        logger.error(f"❌ ملفات مفقودة: {missing_files}")
        return False
    
    logger.info("✅ جميع الملفات المطلوبة موجودة")
    return True

def test_imports():
    """اختبار الاستيرادات"""
    logger.info("🔍 اختبار الاستيرادات...")
    
    critical_imports = [
        ('telegram', 'python-telegram-bot'),
        ('dotenv', 'python-dotenv'),
        ('requests', 'requests'),
        ('flask', 'flask'),
        ('httpx', 'httpx')
    ]
    
    failed_imports = []
    for module, package in critical_imports:
        try:
            __import__(module)
            logger.info(f"✅ {package}")
        except ImportError:
            logger.error(f"❌ {package}")
            failed_imports.append(package)
    
    if failed_imports:
        logger.error(f"❌ فشل في استيراد: {failed_imports}")
        return False
    
    logger.info("✅ جميع الاستيرادات الأساسية تعمل")
    return True

def create_startup_script():
    """إنشاء سكريبت التشغيل"""
    logger.info("📝 إنشاء سكريبت التشغيل...")
    
    startup_content = """#!/bin/bash
# سكريبت تشغيل البوت
echo "🚀 بدء تشغيل البوت..."

# التحقق من Python
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 غير مثبت"
    exit 1
fi

# التحقق من pip
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3 غير مثبت"
    exit 1
fi

# تثبيت المتطلبات
echo "📦 تثبيت المتطلبات..."
pip3 install -r requirements.txt

# تشغيل البوت
echo "🤖 تشغيل البوت..."
python3 main.py
"""
    
    try:
        with open("start_bot.sh", 'w', encoding='utf-8') as f:
            f.write(startup_content)
        
        # جعل الملف قابل للتنفيذ
        os.chmod("start_bot.sh", 0o755)
        
        logger.info("✅ تم إنشاء start_bot.sh")
        return True
        
    except Exception as e:
        logger.error(f"❌ فشل في إنشاء سكريپت التشغيل: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print_header()
    
    success_count = 0
    total_steps = 5
    
    # 1. تثبيت المتطلبات
    if install_requirements():
        success_count += 1
    
    # 2. التحقق من ملف .env
    if check_env_file():
        success_count += 1
    
    # 3. التحقق من الملفات المطلوبة
    if check_required_files():
        success_count += 1
    
    # 4. اختبار الاستيرادات
    if test_imports():
        success_count += 1
    
    # 5. إنشاء سكريپت التشغيل
    if create_startup_script():
        success_count += 1
    
    # النتائج النهائية
    print("\n" + "=" * 50)
    print("📊 نتائج الإعداد:")
    print(f"   ✅ نجح: {success_count}/{total_steps}")
    print(f"   ❌ فشل: {total_steps - success_count}/{total_steps}")
    
    if success_count == total_steps:
        print("\n🎉 تم إعداد البوت بنجاح!")
        print("✅ البوت جاهز للتشغيل")
        print("🚀 يمكنك تشغيل البوت باستخدام: python main.py")
        return 0
    else:
        print("\n⚠️ تم الإعداد مع بعض المشاكل")
        print("🔧 يرجى مراجعة الأخطاء أعلاه وإصلاحها")
        return 1

if __name__ == "__main__":
    sys.exit(main())
