#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل بديل للبوت - للحالات الطارئة
Alternative bot runner - for emergency cases
"""

import os
import sys
import logging
import requests
import threading
import asyncio
from datetime import datetime

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def setup_environment():
    """إعداد البيئة"""
    logger.info("🔧 إعداد البيئة...")
    
    # متغيرات البيئة الأساسية
    env_vars = {
        'BOT_TOKEN': '**********************************************',
        'TELEGRAM_BOT_TOKEN': '**********************************************',
        'SUPABASE_URL': 'https://ytqxxodyecdeosnqoure.supabase.co',
        'SUPABASE_KEY': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4',
        'ADMIN_CHAT_ID': '7513880877',
        'USE_NGROK': 'false',
        'NGROK_ENABLED': 'false',
        'ENVIRONMENT': 'production',
        'DEBUG': 'false',
        'PYTHONUNBUFFERED': '1',
        'PYTHONIOENCODING': 'utf-8',
        'RENDER': 'true',
        'PORT': os.getenv('PORT', '10000'),
        'WEB_SERVER_URL': 'https://1c547fe5.sendaddons.pages.dev'
    }
    
    for key, value in env_vars.items():
        if not os.getenv(key):
            os.environ[key] = str(value)
    
    logger.info("✅ تم إعداد البيئة")

def fix_telegram():
    """إصلاح مشاكل Telegram"""
    try:
        bot_token = os.getenv('BOT_TOKEN')
        if not bot_token:
            return False
            
        logger.info("🔧 إصلاح مشكلة Telegram...")
        
        # مسح webhook
        try:
            response = requests.post(
                f"https://api.telegram.org/bot{bot_token}/deleteWebhook",
                json={"drop_pending_updates": True},
                timeout=10
            )
            if response.status_code == 200:
                logger.info("✅ تم مسح webhook")
        except:
            pass
        
        # مسح التحديثات المعلقة
        try:
            response = requests.get(
                f"https://api.telegram.org/bot{bot_token}/getUpdates",
                params={"offset": -1, "limit": 1, "timeout": 0},
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('ok') and result.get('result'):
                    last_update_id = result['result'][-1]['update_id']
                    requests.get(
                        f"https://api.telegram.org/bot{bot_token}/getUpdates",
                        params={"offset": last_update_id + 1, "limit": 1, "timeout": 0},
                        timeout=10
                    )
                    logger.info("✅ تم مسح التحديثات المعلقة")
        except:
            pass
            
        return True
        
    except Exception as e:
        logger.warning(f"⚠️ تحذير في إصلاح Telegram: {e}")
        return False

def start_health_server():
    """تشغيل خادم الصحة"""
    try:
        from flask import Flask, jsonify
        
        app = Flask(__name__)
        
        @app.route('/')
        def health():
            return jsonify({
                "status": "healthy",
                "service": "minecraft_mods_bot",
                "timestamp": str(datetime.now()),
                "platform": "render"
            })
        
        @app.route('/health')
        def health_check():
            return jsonify({"status": "ok"})
        
        port = int(os.getenv('PORT', 10000))
        
        def run_server():
            app.run(host='0.0.0.0', port=port, debug=False)
        
        server_thread = threading.Thread(target=run_server)
        server_thread.daemon = True
        server_thread.start()
        
        logger.info(f"🌐 خادم الصحة يعمل على المنفذ {port}")
        return True
        
    except Exception as e:
        logger.warning(f"⚠️ فشل في تشغيل خادم الصحة: {e}")
        return False

def run_main_bot():
    """تشغيل البوت الرئيسي"""
    try:
        logger.info("🤖 تشغيل البوت الرئيسي...")
        
        # محاولة تشغيل البوت بطرق مختلفة
        methods = [
            lambda: __import__('main').main(),
            lambda: asyncio.run(__import__('main').main()),
            lambda: asyncio.run(__import__('start_render').main()),
            lambda: __import__('start_render').main()
        ]
        
        for i, method in enumerate(methods, 1):
            try:
                logger.info(f"🔄 محاولة التشغيل {i}...")
                method()
                logger.info("✅ تم تشغيل البوت بنجاح!")
                return True
            except Exception as e:
                logger.warning(f"⚠️ فشلت المحاولة {i}: {e}")
                continue
        
        logger.error("❌ فشل في جميع محاولات التشغيل")
        return False
        
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل البوت: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    try:
        logger.info("🚀 بدء تشغيل البوت - الوضع البديل")
        logger.info(f"⏰ الوقت: {datetime.now()}")
        
        # 1. إعداد البيئة
        setup_environment()
        
        # 2. إصلاح Telegram
        fix_telegram()
        
        # 3. تشغيل خادم الصحة
        start_health_server()
        
        # 4. تشغيل البوت
        success = run_main_bot()
        
        if success:
            logger.info("🎉 البوت يعمل بنجاح!")
            # إبقاء البرنامج يعمل
            try:
                while True:
                    import time
                    time.sleep(60)
                    logger.info("💓 البوت لا يزال يعمل...")
            except KeyboardInterrupt:
                logger.info("⏹️ تم إيقاف البوت")
        else:
            logger.error("❌ فشل في تشغيل البوت")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"❌ خطأ عام: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
