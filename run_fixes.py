#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل سريع لتطبيق جميع الإصلاحات
Quick run to apply all fixes
"""

import os
import sys
import requests
import time

def apply_all_fixes():
    """تطبيق جميع الإصلاحات"""
    print("🚀 تطبيق جميع إصلاحات مشاكل الاستضافة")
    print("=" * 60)
    
    # 1. إصلاح متغيرات البيئة
    print("🔧 إصلاح متغيرات البيئة...")
    env_fixes = {
        'BOT_TOKEN': '**********************************************',
        'TELEGRAM_BOT_TOKEN': '**********************************************',
        'SUPABASE_URL': 'https://ytqxxodyecdeosnqoure.supabase.co',
        'SUPABASE_KEY': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4',
        'ADMIN_CHAT_ID': '7513880877',
        'USE_NGROK': 'false',
        'NGROK_ENABLED': 'false',
        'ENVIRONMENT': 'production',
        'DEBUG': 'false',
        'PYTHONUNBUFFERED': '1',
        'PYTHONIOENCODING': 'utf-8',
        'PORT': '10000'
    }
    
    for key, value in env_fixes.items():
        if not os.getenv(key):
            os.environ[key] = value
            print(f"✅ تم تعيين {key}")
    
    # 2. إصلاح مشكلة Telegram getUpdates
    print("\n🔧 إصلاح مشكلة Telegram getUpdates conflicts...")
    fix_telegram_conflicts()
    
    # 3. اختبار Supabase
    print("\n🔧 اختبار اتصال Supabase...")
    test_supabase_connection()
    
    print("\n✅ تم تطبيق جميع الإصلاحات بنجاح!")
    print("📋 يمكنك الآن تشغيل البوت باستخدام:")
    print("   python main.py")
    print("   أو")
    print("   python start.py")

def fix_telegram_conflicts():
    """إصلاح مشكلة Telegram getUpdates conflicts"""
    try:
        bot_token = os.getenv('BOT_TOKEN')
        if not bot_token:
            print("⚠️ لم يتم العثور على token البوت")
            return False
            
        print("🔧 مسح webhook و التحديثات المعلقة...")
        
        # مسح webhook
        try:
            url = f"https://api.telegram.org/bot{bot_token}/deleteWebhook"
            response = requests.post(url, json={"drop_pending_updates": True}, timeout=10)
            if response.status_code == 200 and response.json().get('ok'):
                print("✅ تم مسح webhook للبوت")
        except Exception as e:
            print(f"⚠️ تحذير في مسح webhook: {e}")
        
        # مسح التحديثات المعلقة
        try:
            url = f"https://api.telegram.org/bot{bot_token}/getUpdates"
            params = {"offset": -1, "limit": 1, "timeout": 0}
            response = requests.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('ok') and result.get('result'):
                    last_update_id = result['result'][-1]['update_id']
                    params['offset'] = last_update_id + 1
                    requests.get(url, params=params, timeout=10)
                    print("✅ تم مسح التحديثات المعلقة")
                else:
                    print("ℹ️ لا توجد تحديثات معلقة")
        except Exception as e:
            print(f"⚠️ تحذير في مسح التحديثات: {e}")
            
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح Telegram: {e}")
        return False

def test_supabase_connection():
    """اختبار اتصال Supabase"""
    try:
        url = os.getenv('SUPABASE_URL')
        key = os.getenv('SUPABASE_KEY')
        
        if not url or not key:
            print("❌ إعدادات Supabase مفقودة")
            return False
            
        headers = {
            'apikey': key,
            'Authorization': f'Bearer {key}'
        }
        
        response = requests.get(f"{url}/rest/v1/", headers=headers, timeout=10)
        
        if response.status_code == 200:
            print("✅ تم الاتصال بـ Supabase بنجاح")
            return True
        else:
            print(f"⚠️ مشكلة في الاتصال بـ Supabase: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"⚠️ تحذير في اختبار Supabase: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    try:
        apply_all_fixes()
        
        # سؤال المستخدم إذا كان يريد تشغيل البوت مباشرة
        print("\n" + "=" * 60)
        choice = input("هل تريد تشغيل البوت الآن؟ (y/n): ").lower().strip()
        
        if choice in ['y', 'yes', 'نعم', '1']:
            print("🚀 تشغيل البوت...")
            
            # محاولة استيراد وتشغيل البوت
            try:
                import main
                print("✅ تم تشغيل البوت بنجاح!")
            except Exception as e:
                print(f"❌ خطأ في تشغيل البوت: {e}")
                print("💡 يمكنك تشغيل البوت يدوياً باستخدام: python main.py")
        else:
            print("👍 تم تطبيق الإصلاحات. يمكنك تشغيل البوت لاحقاً.")
            
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف العملية بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")

if __name__ == "__main__":
    main()
