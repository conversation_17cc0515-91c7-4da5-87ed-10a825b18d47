#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة إصلاح مشاكل الاستضافة
Hosting Issues Fixer

هذا الملف يحل المشاكل الشائعة في الاستضافة
This file fixes common hosting issues
"""

import os
import sys
import subprocess
import logging
import shutil
from pathlib import Path
from typing import List, Dict, Tuple

# إعداد التسجيل
logging.basicConfig(
    format="%(asctime)s - %(levelname)s - %(message)s",
    level=logging.INFO,
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger(__name__)

class HostingIssuesFixer:
    """فئة إصلاح مشاكل الاستضافة"""
    
    def __init__(self):
        self.issues_found = []
        self.fixes_applied = []
        
    def check_python_version(self) -> bool:
        """فحص إصدار Python"""
        logger.info("🐍 فحص إصدار Python...")
        
        version = sys.version_info
        if version.major < 3 or (version.major == 3 and version.minor < 8):
            self.issues_found.append(f"إصدار Python غير مدعوم: {version.major}.{version.minor}")
            logger.error(f"❌ إصدار Python غير مدعوم: {version.major}.{version.minor}")
            return False
        
        logger.info(f"✅ إصدار Python مدعوم: {version.major}.{version.minor}.{version.micro}")
        return True
    
    def fix_pip_issues(self) -> bool:
        """إصلاح مشاكل pip"""
        logger.info("🔧 إصلاح مشاكل pip...")
        
        try:
            # ترقية pip
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", "--upgrade", "pip"
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            
            # تثبيت setuptools و wheel
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", "--upgrade", "setuptools", "wheel"
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            
            self.fixes_applied.append("ترقية pip و setuptools")
            logger.info("✅ تم إصلاح مشاكل pip")
            return True
            
        except subprocess.CalledProcessError as e:
            self.issues_found.append(f"فشل في إصلاح pip: {e}")
            logger.error(f"❌ فشل في إصلاح pip: {e}")
            return False
    
    def fix_ssl_issues(self) -> bool:
        """إصلاح مشاكل SSL"""
        logger.info("🔒 إصلاح مشاكل SSL...")
        
        try:
            # تثبيت certifi
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", "--upgrade", "certifi"
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            
            self.fixes_applied.append("تحديث شهادات SSL")
            logger.info("✅ تم إصلاح مشاكل SSL")
            return True
            
        except subprocess.CalledProcessError as e:
            self.issues_found.append(f"فشل في إصلاح SSL: {e}")
            logger.error(f"❌ فشل في إصلاح SSL: {e}")
            return False
    
    def fix_missing_modules(self) -> bool:
        """إصلاح المكتبات المفقودة"""
        logger.info("📦 إصلاح المكتبات المفقودة...")
        
        critical_packages = [
            'python-dotenv',
            'python-telegram-bot',
            'requests',
            'flask',
            'httpx'
        ]
        
        fixed_count = 0
        
        for package in critical_packages:
            try:
                # محاولة تثبيت المكتبة
                subprocess.check_call([
                    sys.executable, "-m", "pip", "install", package
                ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                
                fixed_count += 1
                logger.info(f"✅ تم تثبيت {package}")
                
            except subprocess.CalledProcessError:
                # محاولة مع --user
                try:
                    subprocess.check_call([
                        sys.executable, "-m", "pip", "install", "--user", package
                    ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                    
                    fixed_count += 1
                    logger.info(f"✅ تم تثبيت {package} (--user)")
                    
                except subprocess.CalledProcessError:
                    logger.warning(f"⚠️ فشل في تثبيت {package}")
        
        if fixed_count > 0:
            self.fixes_applied.append(f"تثبيت {fixed_count} مكتبة مفقودة")
            return True
        
        return False
    
    def fix_requirements_file(self) -> bool:
        """إصلاح ملف requirements.txt"""
        logger.info("📋 فحص ملف requirements.txt...")
        
        requirements_file = Path("requirements.txt")
        
        if not requirements_file.exists():
            logger.info("📝 إنشاء ملف requirements.txt...")
            
            basic_requirements = [
                "python-telegram-bot>=20.0",
                "python-dotenv>=1.0.0",
                "requests>=2.31.0",
                "httpx>=0.24.0",
                "flask>=2.3.0",
                "flask-cors>=4.0.0",
                "supabase>=1.0.0",
                "APScheduler>=3.10.0",
                "cryptography>=41.0.0",
                "Pillow>=10.0.0"
            ]
            
            try:
                with open(requirements_file, 'w', encoding='utf-8') as f:
                    f.write("# متطلبات البوت الأساسية\n")
                    f.write("# Basic Bot Requirements\n\n")
                    for req in basic_requirements:
                        f.write(f"{req}\n")
                
                self.fixes_applied.append("إنشاء ملف requirements.txt")
                logger.info("✅ تم إنشاء ملف requirements.txt")
                return True
                
            except Exception as e:
                self.issues_found.append(f"فشل في إنشاء requirements.txt: {e}")
                logger.error(f"❌ فشل في إنشاء requirements.txt: {e}")
                return False
        
        logger.info("✅ ملف requirements.txt موجود")
        return True
    
    def fix_env_file(self) -> bool:
        """إصلاح ملف .env"""
        logger.info("🔧 فحص ملف .env...")
        
        env_file = Path(".env")
        env_example = Path(".env.example")
        
        if not env_file.exists():
            if env_example.exists():
                try:
                    shutil.copy2(env_example, env_file)
                    self.fixes_applied.append("نسخ .env.example إلى .env")
                    logger.info("✅ تم نسخ .env.example إلى .env")
                    logger.warning("⚠️ يرجى تحديث المتغيرات في ملف .env")
                    return True
                except Exception as e:
                    self.issues_found.append(f"فشل في نسخ .env.example: {e}")
                    return False
            else:
                logger.warning("⚠️ ملف .env غير موجود ولا يوجد .env.example")
                self.issues_found.append("ملف .env مفقود")
                return False
        
        logger.info("✅ ملف .env موجود")
        return True
    
    def fix_permissions(self) -> bool:
        """إصلاح صلاحيات الملفات"""
        logger.info("🔐 فحص صلاحيات الملفات...")
        
        script_files = [
            "main.py",
            "setup_bot.py", 
            "install_requirements.py",
            "check_dependencies.py"
        ]
        
        fixed_count = 0
        
        for script in script_files:
            script_path = Path(script)
            if script_path.exists():
                try:
                    # إضافة صلاحية التنفيذ
                    os.chmod(script_path, 0o755)
                    fixed_count += 1
                except Exception as e:
                    logger.warning(f"⚠️ فشل في تعديل صلاحيات {script}: {e}")
        
        if fixed_count > 0:
            self.fixes_applied.append(f"إصلاح صلاحيات {fixed_count} ملف")
            logger.info(f"✅ تم إصلاح صلاحيات {fixed_count} ملف")
        
        return True
    
    def install_with_fallback(self) -> bool:
        """تثبيت المتطلبات مع خيارات احتياطية"""
        logger.info("📦 تثبيت المتطلبات مع خيارات احتياطية...")
        
        install_commands = [
            [sys.executable, "-m", "pip", "install", "-r", "requirements.txt"],
            [sys.executable, "-m", "pip", "install", "--user", "-r", "requirements.txt"],
            [sys.executable, "-m", "pip", "install", "--no-cache-dir", "-r", "requirements.txt"],
            [sys.executable, "-m", "pip", "install", "--trusted-host", "pypi.org", 
             "--trusted-host", "pypi.python.org", "--trusted-host", "files.pythonhosted.org", 
             "-r", "requirements.txt"]
        ]
        
        for i, cmd in enumerate(install_commands, 1):
            try:
                logger.info(f"🔄 محاولة التثبيت {i}/{len(install_commands)}...")
                subprocess.check_call(cmd, timeout=300)
                
                self.fixes_applied.append(f"تثبيت المتطلبات (الطريقة {i})")
                logger.info("✅ تم تثبيت المتطلبات بنجاح")
                return True
                
            except (subprocess.CalledProcessError, subprocess.TimeoutExpired) as e:
                logger.warning(f"⚠️ فشلت الطريقة {i}: {e}")
                continue
        
        self.issues_found.append("فشل في تثبيت المتطلبات بجميع الطرق")
        logger.error("❌ فشل في تثبيت المتطلبات")
        return False
    
    def run_diagnostics(self) -> Dict[str, bool]:
        """تشغيل التشخيص الشامل"""
        logger.info("🔍 تشغيل التشخيص الشامل...")
        
        results = {
            'python_version': self.check_python_version(),
            'pip_issues': self.fix_pip_issues(),
            'ssl_issues': self.fix_ssl_issues(),
            'requirements_file': self.fix_requirements_file(),
            'env_file': self.fix_env_file(),
            'permissions': self.fix_permissions(),
            'missing_modules': self.fix_missing_modules(),
            'install_requirements': self.install_with_fallback()
        }
        
        return results
    
    def print_summary(self, results: Dict[str, bool]):
        """طباعة ملخص النتائج"""
        print("\n" + "=" * 60)
        print("📊 ملخص إصلاح مشاكل الاستضافة")
        print("=" * 60)
        
        success_count = sum(results.values())
        total_count = len(results)
        
        print(f"\n✅ نجح: {success_count}/{total_count}")
        print(f"❌ فشل: {total_count - success_count}/{total_count}")
        
        if self.fixes_applied:
            print(f"\n🔧 الإصلاحات المطبقة:")
            for fix in self.fixes_applied:
                print(f"   ✅ {fix}")
        
        if self.issues_found:
            print(f"\n⚠️ المشاكل المتبقية:")
            for issue in self.issues_found:
                print(f"   ❌ {issue}")
        
        if success_count == total_count:
            print(f"\n🎉 تم إصلاح جميع المشاكل! البوت جاهز للتشغيل.")
        else:
            print(f"\n⚠️ بعض المشاكل تحتاج إلى إصلاح يدوي.")

def main():
    """الدالة الرئيسية"""
    print("🔧 أداة إصلاح مشاكل الاستضافة")
    print("=" * 50)
    
    fixer = HostingIssuesFixer()
    results = fixer.run_diagnostics()
    fixer.print_summary(results)
    
    return 0 if all(results.values()) else 1

if __name__ == "__main__":
    sys.exit(main())
