#!/usr/bin/env python3
"""
ملف تشغيل مبسط جداً للبوت
Ultra-simplified bot runner
"""

import os
import sys

# إعداد متغيرات البيئة الأساسية
os.environ.setdefault('BOT_TOKEN', '7605181405:AAEBua03X_QdD6GkCqKWR1P02EtXIbnSkx4')
os.environ.setdefault('SUPABASE_URL', 'https://ytqxxodyecdeosnqoure.supabase.co')
os.environ.setdefault('SUPABASE_KEY', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4')
os.environ.setdefault('ADMIN_CHAT_ID', '7513880877')
os.environ.setdefault('USE_NGROK', 'false')
os.environ.setdefault('ENVIRONMENT', 'production')
os.environ.setdefault('PYTHONUNBUFFERED', '1')

print("🚀 بدء تشغيل البوت...")

# إصلاح سريع لـ Telegram
try:
    import requests
    bot_token = os.getenv('BOT_TOKEN')
    if bot_token:
        # مسح webhook والتحديثات المعلقة
        requests.post(f"https://api.telegram.org/bot{bot_token}/deleteWebhook", 
                     json={"drop_pending_updates": True}, timeout=5)
        print("✅ تم إصلاح Telegram")
except:
    pass

# تشغيل خادم الصحة
try:
    from flask import Flask, jsonify
    import threading
    
    app = Flask(__name__)
    
    @app.route('/')
    def health():
        return jsonify({"status": "healthy", "service": "minecraft_mods_bot"})
    
    port = int(os.getenv('PORT', 10000))
    
    def run_server():
        app.run(host='0.0.0.0', port=port, debug=False)
    
    server_thread = threading.Thread(target=run_server)
    server_thread.daemon = True
    server_thread.start()
    
    print(f"🌐 خادم الصحة يعمل على المنفذ {port}")
except:
    print("⚠️ فشل في تشغيل خادم الصحة")

# تشغيل البوت
try:
    print("🤖 تشغيل البوت الرئيسي...")
    import main
    print("✅ تم تشغيل البوت بنجاح!")
except Exception as e:
    print(f"❌ خطأ: {e}")
    try:
        import asyncio
        asyncio.run(main.main())
        print("✅ تم تشغيل البوت بنجاح!")
    except Exception as e2:
        print(f"❌ خطأ ثاني: {e2}")
        sys.exit(1)
