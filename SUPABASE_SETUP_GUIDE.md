# 🗄️ دليل إعداد قاعدة البيانات في Supabase
# Supabase Database Setup Guide

## 📋 الخطوات المطلوبة لإنشاء الجداول

### 1. الدخول إلى Supabase Dashboard
1. اذهب إلى [Supabase Dashboard](https://app.supabase.com)
2. سجل الدخول إلى حسابك
3. اختر مشروعك أو أنشئ مشروع جديد

### 2. فتح SQL Editor
1. من القائمة الجانبية، اختر **SQL Editor**
2. انقر على **New Query** لإنشاء استعلام جديد

### 3. تنفيذ كود إنشاء الجداول

#### الخطوة الأولى: إنشاء الجداول الأساسية
```sql
-- انسخ والصق محتوى ملف create_all_tables_fixed.sql هنا
-- Copy and paste the content of create_all_tables_fixed.sql here
```

1. افتح ملف `create_all_tables_fixed.sql` (الملف المحدث)
2. انسخ المحتوى كاملاً
3. الصقه في SQL Editor
4. انقر على **Run** لتنفيذ الكود

**ملاحظة مهمة:** استخدم `create_all_tables_fixed.sql` بدلاً من `create_all_tables.sql` لأنه يحتوي على التصحيحات المطلوبة.

#### الخطوة الثانية: إدراج البيانات التجريبية (اختياري)
```sql
-- انسخ والصق محتوى ملف insert_sample_data.sql هنا
-- Copy and paste the content of insert_sample_data.sql here
```

1. افتح ملف `insert_sample_data.sql`
2. انسخ المحتوى كاملاً
3. الصقه في SQL Editor جديد
4. انقر على **Run** لتنفيذ الكود

### 4. التحقق من إنشاء الجداول

#### فحص الجداول المنشأة:
```sql
-- عرض جميع الجداول
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_name;
```

#### فحص البيانات التجريبية:
```sql
-- فحص المودات
SELECT COUNT(*) as total_mods FROM public.mods;

-- فحص الإشعارات
SELECT COUNT(*) as total_notifications FROM public.saved_notifications;

-- فحص المهام
SELECT COUNT(*) as total_tasks FROM public.tasks;

-- عرض إحصائيات شاملة
SELECT * FROM get_system_overview();
```

## 📊 الجداول التي سيتم إنشاؤها

### 1. الجداول الأساسية:
- ✅ `mods` - جدول المودات الرئيسي
- ✅ `system_stats` - إحصائيات النظام

### 2. جداول نظام الإشعارات:
- ✅ `saved_notifications` - الإشعارات المحفوظة
- ✅ `notification_broadcasts` - سجل الإرسال
- ✅ `notification_user_logs` - تفاصيل الإرسال لكل مستخدم

### 3. جداول نظام المهام:
- ✅ `tasks` - المهام المتاحة
- ✅ `user_task_completions` - إنجازات المستخدمين

### 4. جداول نظام الإعلانات:
- ✅ `user_ads_settings` - إعدادات الإعلانات
- ✅ `ads_stats` - إحصائيات الإعلانات

### 5. جداول تقصير الروابط:
- ✅ `user_url_shortener_settings` - إعدادات تقصير الروابط
- ✅ `url_shortener_stats` - إحصائيات الروابط

### 6. جداول تخصيص الصفحات:
- ✅ `user_page_customization` - تخصيصات المستخدمين

### 7. جداول الروابط المخصصة:
- ✅ `custom_download_links` - روابط التحميل المخصصة

## 🔧 الميزات المضافة

### 1. الفهارس (Indexes):
- فهارس لتحسين أداء الاستعلامات
- فهارس على الحقول المستخدمة بكثرة

### 2. المشغلات (Triggers):
- تحديث تلقائي لحقل `updated_at`
- تتبع التغييرات في الجداول

### 3. الدوال المساعدة:
- `get_system_overview()` - إحصائيات شاملة
- `increment_download_count()` - تحديث عداد التحميلات
- `update_updated_at_column()` - تحديث الوقت تلقائياً

### 4. العروض (Views):
- `mods_stats` - إحصائيات المودات
- `active_users_stats` - إحصائيات المستخدمين
- `recent_notifications` - أحدث الإشعارات

### 5. الأمان (Security):
- Row Level Security (RLS) مفعل على الجداول الحساسة
- صلاحيات محددة للوصول للبيانات

## 🚨 ملاحظات مهمة

### 1. النسخ الاحتياطي:
```sql
-- إنشاء نسخة احتياطية قبل التنفيذ
-- Create backup before execution
pg_dump your_database > backup_before_setup.sql
```

### 2. التحقق من الأذونات:
- تأكد من أن لديك صلاحيات إنشاء الجداول
- تأكد من أن مساحة التخزين كافية

### 3. في حالة الأخطاء:
```sql
-- حذف جدول معين إذا حدث خطأ
DROP TABLE IF EXISTS table_name CASCADE;

-- حذف جميع الجداول (احذر!)
-- DROP SCHEMA public CASCADE;
-- CREATE SCHEMA public;
```

## 🔍 اختبار النظام

### 1. اختبار إدراج البيانات:
```sql
-- إدراج مود تجريبي
INSERT INTO public.mods (name, description, description_ar, category, version, download_url, image_urls)
VALUES ('Test Mod', 'Test Description', 'وصف تجريبي', 'addons', '1.21+', 'https://example.com/test.mcpack', ARRAY['https://example.com/test.jpg']);
```

### 2. اختبار الاستعلامات:
```sql
-- البحث في المودات
SELECT * FROM public.mods WHERE category = 'addons';

-- عرض الإحصائيات
SELECT * FROM mods_stats;
```

### 3. اختبار الدوال:
```sql
-- اختبار دالة الإحصائيات
SELECT * FROM get_system_overview();

-- اختبار تحديث عداد التحميلات
SELECT increment_download_count(1);
```

## ✅ التأكد من نجاح الإعداد

بعد تنفيذ جميع الخطوات، يجب أن ترى:

1. ✅ جميع الجداول منشأة بنجاح
2. ✅ الفهارس والمشغلات تعمل
3. ✅ البيانات التجريبية مدرجة (إذا اخترت إدراجها)
4. ✅ الدوال والعروض متاحة
5. ✅ البوت يتصل بقاعدة البيانات بدون أخطاء

## 🎉 الخطوة التالية

بعد إنشاء الجداول بنجاح:

1. شغل البوت: `python main.py`
2. تحقق من السجلات للتأكد من عدم وجود أخطاء
3. اختبر وظائف البوت المختلفة
4. راقب الأداء والإحصائيات

---

**ملاحظة:** إذا واجهت أي مشاكل، راجع سجلات Supabase أو اتصل بالدعم الفني.
