#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نهائي شامل للبوت
"""

import os
import sys
import logging
import requests
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

# إعداد التسجيل
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_environment():
    """اختبار متغيرات البيئة"""
    logger.info("🔍 اختبار متغيرات البيئة...")
    
    required_vars = [
        'BOT_TOKEN', 'SUPABASE_URL', 'SUPABASE_KEY',
        'USER_SUPABASE_URL', 'USER_SUPABASE_KEY', 'ADMIN_CHAT_ID'
    ]
    
    missing = []
    for var in required_vars:
        if not os.getenv(var):
            missing.append(var)
    
    if missing:
        logger.error(f"❌ متغيرات مفقودة: {', '.join(missing)}")
        return False
    else:
        logger.info("✅ جميع متغيرات البيئة موجودة")
        return True

def test_bot_connection():
    """اختبار اتصال البوت"""
    logger.info("🔗 اختبار اتصال البوت...")
    
    token = os.getenv('BOT_TOKEN')
    if not token:
        logger.error("❌ رمز البوت مفقود")
        return False
    
    try:
        url = f"https://api.telegram.org/bot{token}/getMe"
        response = requests.get(url, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('ok'):
                bot_info = result.get('result', {})
                username = bot_info.get('username', 'Unknown')
                logger.info(f"✅ البوت متصل: @{username}")
                return True
        
        logger.error(f"❌ فشل اتصال البوت: {response.status_code}")
        return False
        
    except Exception as e:
        logger.error(f"❌ خطأ في اتصال البوت: {e}")
        return False

def test_databases():
    """اختبار قواعد البيانات"""
    logger.info("🗄️ اختبار قواعد البيانات...")
    
    # اختبار قاعدة البيانات الرئيسية
    main_db = test_main_database()
    
    # اختبار قاعدة بيانات المستخدمين
    user_db = test_user_database()
    
    return main_db and user_db

def test_main_database():
    """اختبار قاعدة البيانات الرئيسية"""
    url = os.getenv('SUPABASE_URL')
    key = os.getenv('SUPABASE_KEY')
    
    if not url or not key:
        logger.error("❌ إعدادات قاعدة البيانات الرئيسية مفقودة")
        return False
    
    try:
        headers = {
            'apikey': key,
            'Authorization': f'Bearer {key}',
            'Content-Type': 'application/json'
        }
        
        test_url = f"{url}/rest/v1/mods?limit=1"
        response = requests.get(test_url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            logger.info("✅ قاعدة البيانات الرئيسية متصلة")
            return True
        else:
            logger.error(f"❌ فشل اتصال قاعدة البيانات الرئيسية: {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"❌ خطأ في قاعدة البيانات الرئيسية: {e}")
        return False

def test_user_database():
    """اختبار قاعدة بيانات المستخدمين"""
    url = os.getenv('USER_SUPABASE_URL')
    key = os.getenv('USER_SUPABASE_KEY')
    
    if not url or not key:
        logger.error("❌ إعدادات قاعدة بيانات المستخدمين مفقودة")
        return False
    
    try:
        headers = {
            'apikey': key,
            'Authorization': f'Bearer {key}',
            'Content-Type': 'application/json'
        }
        
        test_url = f"{url}/rest/v1/users?limit=1"
        response = requests.get(test_url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            logger.info("✅ قاعدة بيانات المستخدمين متصلة")
            return True
        else:
            logger.error(f"❌ فشل اتصال قاعدة بيانات المستخدمين: {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"❌ خطأ في قاعدة بيانات المستخدمين: {e}")
        return False

def test_page_customization():
    """اختبار ميزات تخصيص الصفحة"""
    logger.info("🎨 اختبار ميزات تخصيص الصفحة...")
    
    # فحص ملف HTML
    html_ok = test_html_file()
    
    # فحص مجلد الصور
    images_ok = test_images_folder()
    
    # فحص جدول التخصيص
    table_ok = test_customization_table()
    
    return html_ok and images_ok and table_ok

def test_html_file():
    """فحص ملف HTML"""
    if os.path.exists("mod_details.html"):
        logger.info("✅ ملف صفحة العرض موجود")
        return True
    else:
        logger.error("❌ ملف صفحة العرض مفقود")
        return False

def test_images_folder():
    """فحص مجلد الصور"""
    try:
        htdocs_path = "htdocs"
        images_path = os.path.join(htdocs_path, "uploaded_images")
        
        if not os.path.exists(htdocs_path):
            os.makedirs(htdocs_path)
        
        if not os.path.exists(images_path):
            os.makedirs(images_path)
        
        logger.info("✅ مجلد الصور جاهز")
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في مجلد الصور: {e}")
        return False

def test_customization_table():
    """فحص جدول التخصيص"""
    url = os.getenv('SUPABASE_URL')
    key = os.getenv('SUPABASE_KEY')
    
    if not url or not key:
        logger.warning("⚠️ لا يمكن فحص جدول التخصيص - إعدادات مفقودة")
        return True  # لا نعتبره فشل
    
    try:
        headers = {
            'apikey': key,
            'Authorization': f'Bearer {key}',
            'Content-Type': 'application/json'
        }
        
        test_url = f"{url}/rest/v1/page_customization_settings?limit=1"
        response = requests.get(test_url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            logger.info("✅ جدول التخصيص متاح")
            return True
        else:
            logger.warning(f"⚠️ جدول التخصيص غير متاح: {response.status_code}")
            return True  # لا نعتبره فشل
            
    except Exception as e:
        logger.warning(f"⚠️ تحذير في جدول التخصيص: {e}")
        return True  # لا نعتبره فشل

def test_render_readiness():
    """اختبار جاهزية Render"""
    logger.info("🔧 اختبار جاهزية Render...")
    
    # فحص متغيرات Render
    render_vars = ['PYTHONUNBUFFERED', 'USE_POLLING']
    missing_render_vars = []
    
    for var in render_vars:
        if not os.getenv(var):
            missing_render_vars.append(var)
    
    if missing_render_vars:
        logger.warning(f"⚠️ متغيرات Render مفقودة: {', '.join(missing_render_vars)}")
    else:
        logger.info("✅ متغيرات Render محددة")
    
    # فحص ملف الإصلاح
    if os.path.exists("render_fix.py"):
        logger.info("✅ ملف إصلاح Render موجود")
        return True
    else:
        logger.warning("⚠️ ملف إصلاح Render مفقود")
        return True  # لا نعتبره فشل

def test_imports():
    """اختبار الاستيرادات"""
    logger.info("📦 اختبار الاستيرادات...")
    
    required_modules = ['telegram', 'requests', 'dotenv']
    failed_imports = []
    
    for module in required_modules:
        try:
            if module == 'dotenv':
                import dotenv
            else:
                __import__(module)
        except ImportError:
            failed_imports.append(module)
    
    if failed_imports:
        logger.error(f"❌ فشل استيراد: {', '.join(failed_imports)}")
        return False
    else:
        logger.info("✅ جميع الاستيرادات متاحة")
        return True

def run_all_tests():
    """تشغيل جميع الاختبارات"""
    logger.info("🚀 بدء الاختبار الشامل للبوت")
    logger.info("=" * 50)
    
    tests = [
        ("متغيرات البيئة", test_environment),
        ("الاستيرادات", test_imports),
        ("اتصال البوت", test_bot_connection),
        ("قواعد البيانات", test_databases),
        ("ميزات تخصيص الصفحة", test_page_customization),
        ("جاهزية Render", test_render_readiness),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 {test_name}")
        logger.info("-" * 30)
        
        try:
            if test_func():
                passed += 1
        except Exception as e:
            logger.error(f"💥 خطأ في {test_name}: {e}")
    
    # النتيجة النهائية
    logger.info("\n" + "=" * 50)
    logger.info("📊 النتيجة النهائية")
    logger.info("=" * 50)
    
    success_rate = (passed / total) * 100
    logger.info(f"✅ نجح: {passed}/{total} ({success_rate:.1f}%)")
    
    if success_rate >= 80:
        logger.info("🎉 البوت جاهز للعمل!")
        status = "ممتاز"
    elif success_rate >= 60:
        logger.info("✅ البوت يعمل مع بعض التحذيرات")
        status = "جيد"
    else:
        logger.info("⚠️ البوت يحتاج لإصلاحات")
        status = "يحتاج إصلاح"
    
    return success_rate >= 60

def main():
    """الدالة الرئيسية"""
    print("🔍 اختبار نهائي شامل للبوت")
    print("=" * 40)
    
    success = run_all_tests()
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
